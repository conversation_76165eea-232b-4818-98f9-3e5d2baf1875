package com.yxt.talent.rv.application.authprj;

import com.alibaba.fastjson.JSON;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthPrjUserOverviewMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjRuleLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjRuleLevelPO;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * 认证项目分层结果计算服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthPrjLevelResultService {


    private final AuthprjRuleLevelMapper authprjRuleLevelMapper;

    private final AuthprjResultUserMapper authprjResultUserMapper;
    private final AuthPrjUserOverviewMapper authprjUserOverviewMapper;
    private final SpRuleService spRuleService;
    private final AuthPrjIndicatorResultService authPrjIndicatorResultService;

    /**
     * 计算用户分层结果
     *
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @param userId 用户ID
     */
    public void calculateUserLevelResult(String orgId, String authprjId, String userId) {
        try {
            log.info("LOG10040:开始计算用户分层结果, authprjId={}, userId={}", authprjId, userId);

            // 1. 查询认证项目的分层规则
            List<AuthprjRuleLevelPO> ruleLevels = authprjRuleLevelMapper.selectByAuthprjId(orgId, authprjId);
            if (CollectionUtils.isEmpty(ruleLevels)) {
                log.warn("LOG10041:未配置分层规则, authprjId={}", authprjId);
                return;
            }

            // 2. 转换为规则计算所需的格式
            List<AuthprjRuleLevelDTO> ruleLevelDTOs = convertToRuleLevelDTOs(ruleLevels);

            // 3. 使用spRuleService进行规则匹配计算
            RuleMainBase ruleMainBase = new RuleMainBase();
            ruleMainBase.setOrgId(orgId);
            ruleMainBase.setBizId(authprjId);

            List<String> userIds = Collections.singletonList(userId);

            spRuleService.calcFirstMatch(ruleMainBase, userIds, ruleLevelDTOs,
                uid -> uid,
                AuthprjRuleLevelDTO::getRuleBean,
                (uid, matchedLevel) -> {
                    if (matchedLevel == null) {
                        log.info("LOG42026:未匹配到分层规则, authprjId={}, userId={}", authprjId, uid);
                    }
                    saveUserAuthResult(orgId, authprjId, uid, matchedLevel);
                }, true);
            log.info("LOG10042:计算用户分层结果完成, authprjId={}, userId={}", authprjId, userId);

        } catch (Exception e) {
            log.error("LOG10043:计算用户分层结果失败, authprjId={}, userId={}, error={}",
                authprjId, userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清理指定用户的认证项目分层结果（仅清理自动计算结果）
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     */
    public void deleteUserLevelResult(String orgId, String authprjId, String userId) {
        authprjResultUserMapper.deleteByAuthPrjAndUser(orgId, authprjId, userId);
        log.info("LOG42346:已清理用户认证项目历史分层结果, authprjId={}, userId={}", authprjId, userId);
    }

    /**
     * 转换为规则计算所需的DTO格式
     */
    private List<AuthprjRuleLevelDTO> convertToRuleLevelDTOs(List<AuthprjRuleLevelPO> ruleLevels) {
        return ruleLevels.stream().map(level -> {
            AuthprjRuleLevelDTO dto = new AuthprjRuleLevelDTO();
            BeanCopierUtil.copy(level, dto);
            if (StringUtils.isNotBlank(level.getFormula())) {
                dto.setRuleBean(JSON.parseObject(level.getFormula(), SpRuleBean.class));
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 保存用户认证结果
     */
    private void saveUserAuthResult(String orgId, String authprjId, String userId, @Nullable AuthprjRuleLevelDTO matchedLevel) {
        try {
            String operator = YxtBasicUtils.userInfo().getUserId();
            operator = StringUtils.isBlank(operator) ? AppConstants.CODE_OPERATOR_ID : operator;
            LocalDateTime now = LocalDateTime.now();

            // 查询现有记录
            AuthprjResultUserPO existingResult = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authprjId, userId);

            if (existingResult != null && existingResult.getManualFlag() == 1) {
                log.info("LOG10007:手动修改了结果，这里不做覆盖");
                return;
            }

            AuthprjResultUserPO userResult = new AuthprjResultUserPO();
            if (existingResult != null) {
                userResult.setId(existingResult.getId());
                userResult.setCreateTime(existingResult.getCreateTime());
                userResult.setCreateUserId(existingResult.getCreateUserId());
            } else {
                userResult.setId(ApiUtil.getUuid());
                userResult.setCreateTime(now);
                userResult.setCreateUserId(operator);
            }

            userResult.setOrgId(orgId);
            userResult.setAuthprjId(authprjId);
            userResult.setUserId(userId);
            BigDecimal scoreValue = authPrjIndicatorResultService.calculateUserTotalScore(orgId, authprjId, userId);
            userResult.setScoreValue(scoreValue);
            userResult.setAuthTime(now);
            userResult.setManualFlag(0); // 自动计算
            userResult.setDeleted(0);
            userResult.setUpdateTime(now);
            userResult.setUpdateUserId(operator);

            // 使用现有的进度计算方法来判断任务完成状态
            BigDecimal progress = authprjUserOverviewMapper.calculateUserProgress(orgId, authprjId, userId);
            if (progress == null) {
                log.debug("LOG10047:用户进度为空, authprjId={}, userId={}", authprjId, userId);
                progress = ZERO;
            }

            int isCompleted = progress.compareTo(valueOf(100)) >= 0 ? 1 : 0;
            userResult.setTaskCompleted(isCompleted);

            // 没有匹配到任何分层时，或者进度为0时，设置为空
            userResult.setLevelId(matchedLevel == null || Objects.equals(progress, ZERO) ? EMPTY : matchedLevel.getId());

            authprjResultUserMapper.insertOnDuplicateUpdate(userResult);

            log.info("LOG10044:保存用户认证结果完成, authprjId={}, userId={}, levelId={}, scoreValue={}",
                authprjId, userId, userResult.getLevelId(), scoreValue);

        } catch (Exception e) {
            log.error("LOG10045:保存用户认证结果失败, authprjId={}, userId={}, error={}", 
                authprjId, userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 认证规则分层DTO
     */
    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AuthprjRuleLevelDTO {
        // Getters and setters
        private String id;
        private String levelName;
        private SpRuleBean ruleBean;
    }
}
