package com.yxt.talent.rv.application.authprj;

import com.yxt.common.repo.RedisRepository;
import com.yxt.talent.rv.application.authprj.lifecycle.AuthPrjCalcTaskLifecycle;
import com.yxt.talent.rv.application.authprj.lifecycle.AuthPrjCalcTaskStateFactory;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskProgress;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskState;
import com.yxt.talent.rv.infrastructure.service.taskprogress.storage.TaskProgressStorage;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.yxt.talent.rv.application.authprj.lifecycle.AuthPrjCalcTaskStateFactory.STATE_CALCULATING;
import static com.yxt.talent.rv.application.authprj.lifecycle.AuthPrjCalcTaskStateFactory.STATE_COMPLETED;
import static com.yxt.talent.rv.application.authprj.lifecycle.AuthPrjCalcTaskStateFactory.STATE_FAILED;

/**
 * 认证项目计算任务状态管理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthPrjCalcTaskService {
    
    private final TaskProgressStorage taskProgressStorage;
    private final AuthPrjCalcTaskStateFactory stateFactory;
    private final RedisRepository talentRedisRepository;

    /**
     * 异步计算用户计数器Key模板
     */
    private static final String ASYNC_COUNTER_KEY = "sprv:auth_prj_calc:async_counter:%s";

    /**
     * 异步计算失败计数器Key模板
     */
    private static final String ASYNC_FAILURE_COUNTER_KEY = "sprv:auth_prj_calc:async_failure_counter:%s";

    /**
     * 当前计算ID Key模板 - 用于标识项目当前活跃的计算任务
     */
    private static final String CURRENT_CALC_ID_KEY = "sprv:auth_prj_calc:current_calc_id:%s:%s";

    /**
     * 计数器过期时间（小时）
     */
    private static final int COUNTER_EXPIRE_HOURS = 24;

    /**
     * 当前计算ID过期时间（小时）
     */
    private static final int CURRENT_CALC_ID_EXPIRE_HOURS = 48;

    /**
     * 获取当前活跃的计算ID
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @return 当前计算ID，如果没有则返回null
     */
    @Nullable
    public String getCurrentCalcId(String orgId, String authPrjId) {
        String key = String.format(CURRENT_CALC_ID_KEY, orgId, authPrjId);
        return talentRedisRepository.opsForValue().get(key);
    }

    /**
     * 设置当前活跃的计算ID
     *
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @param traceId 新的计算ID
     */
    public void setCurrentCalcId(String orgId, String authPrjId, String traceId) {
        String key = String.format(CURRENT_CALC_ID_KEY, orgId, authPrjId);
        talentRedisRepository.opsForValue().set(key, traceId, CURRENT_CALC_ID_EXPIRE_HOURS, java.util.concurrent.TimeUnit.HOURS);
        log.info("LOG41016:设置当前计算ID: orgId={}, authPrjId={}, traceId={}", orgId, authPrjId, traceId);
    }
    
    /**
     * 开始计算任务
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @param traceId 计算追踪ID（作为任务唯一标识）
     * @param asyncUserCount 异步用户数量，0表示同步计算
     */
    public void startCalculation(String orgId, String authPrjId, String traceId, int asyncUserCount) {
        String taskId = buildTaskId(orgId, authPrjId, traceId);
        TaskState calculatingState = stateFactory.getStateByCode(STATE_CALCULATING);
        
        TaskProgress progress = TaskProgress.builder()
                .taskType(AuthPrjCalcTaskLifecycle.TASK_TYPE)
                .taskId(taskId)
                .stateCode(calculatingState.getCode())
                .message("开始计算认证项目结果")
                .timestamp(LocalDateTime.now())
                .extraData(traceId)
                .build();
        
        taskProgressStorage.save(progress);

        // 设置当前活跃的计算ID
        setCurrentCalcId(orgId, authPrjId, traceId);
        
        // 如果是异步计算，初始化用户计数器
        if (asyncUserCount > 0) {
            initAsyncCounters(traceId, asyncUserCount);
        }

        log.info("LOG41056:开始计算任务: orgId={}, authPrjId={}, traceId={}, asyncUserCount={}",
                orgId, authPrjId, traceId, asyncUserCount);
    }

    /**
     * 递减异步用户计数器，并在计数器归零时完成任务
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @param traceId 计算追踪ID
     * @param userId 用户ID
     * @param success 处理是否成功
     */
    public void decreaseAndCompleteAsync(String orgId, String authPrjId, String traceId, String userId, boolean success) {
        String counterKey = String.format(ASYNC_COUNTER_KEY, traceId);
        String failureCounterKey = String.format(ASYNC_FAILURE_COUNTER_KEY, traceId);

        try {
            // 如果当前子任务失败，则增加失败计数器
            if (!success) {
                talentRedisRepository.opsForValue().increment(failureCounterKey);
            }

            // 递减主计数器
            Long remainingCount = talentRedisRepository.opsForValue().decrement(counterKey);

            log.info(
                "LOG41086:异步计算用户完成: orgId={}, authPrjId={}, traceId={}, userId={}, success={}, remainingCount={}",
                    orgId, authPrjId, traceId, userId, success, remainingCount);

            // 如果计数器归零，表示所有用户都处理完成
            if (remainingCount != null && remainingCount <= 0) {
                // 检查失败计数器
                String failureCountStr = talentRedisRepository.opsForValue().get(failureCounterKey);
                int failureCount = (failureCountStr == null) ? 0 : Integer.parseInt(failureCountStr);

                boolean overallSuccess = (failureCount == 0);
                String message = overallSuccess ? "异步计算全部完成" : "异步计算部分失败，失败数: " + failureCount;

                // 清理计数器
                talentRedisRepository.delete(counterKey);
                talentRedisRepository.delete(failureCounterKey);

                // 标记任务完成
                completeCalculation(orgId, authPrjId, traceId, overallSuccess, message);

                log.info("LOG41096:异步计算任务全部完成: orgId={}, authPrjId={}, traceId={}, overallSuccess={}", 
                        orgId, authPrjId, traceId, overallSuccess);
            }
        } catch (Exception e) {
            log.error("LOG41106:递减异步计数器失败: orgId={}, authPrjId={}, traceId={}, userId={}",
                    orgId, authPrjId, traceId, userId, e);
        }
    }

    /**
     * 初始化异步计算用户计数器
     *
     * @param traceId 计算追踪ID
     * @param userCount 用户数量
     */
    /**
     * 初始化异步计算相关计数器
     *
     * @param traceId 计算追踪ID
     * @param userCount 用户数量
     */
    private void initAsyncCounters(String traceId, int userCount) {
        String counterKey = String.format(ASYNC_COUNTER_KEY, traceId);
        String failureCounterKey = String.format(ASYNC_FAILURE_COUNTER_KEY, traceId);

        // 初始化主计数器
        talentRedisRepository.opsForValue().set(counterKey, String.valueOf(userCount), COUNTER_EXPIRE_HOURS, java.util.concurrent.TimeUnit.HOURS);
        // 初始化失败计数器
        talentRedisRepository.opsForValue().set(failureCounterKey, "0", COUNTER_EXPIRE_HOURS, java.util.concurrent.TimeUnit.HOURS);

        log.info("LOG41046:初始化异步计算计数器: traceId={}, userCount={}", traceId, userCount);
    }
    
    /**
     * 完成计算任务
     *
     * @param traceId 计算追踪ID
     * @param success 是否成功
     * @param message 完成消息
     */
    public void completeCalculation(String orgId, String authPrjId, String traceId, boolean success, String message) {
        String taskId = buildTaskId(orgId, authPrjId, traceId);
        String stateCode = success ? STATE_COMPLETED : STATE_FAILED;
        
        TaskProgress progress = TaskProgress.builder()
                .taskType(AuthPrjCalcTaskLifecycle.TASK_TYPE)
                .taskId(taskId)
                .stateCode(stateCode)
                .message(message)
                .timestamp(LocalDateTime.now())
                .extraData(traceId)
                .build();
        
        taskProgressStorage.save(progress);
        log.info("LOG41076:完成计算任务: traceId={}, success={}, message={}", traceId, success, message);
    }

    /**
     * 获取计算进度（基于当前活跃的计算ID）
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @return 任务进度，如果不存在返回null
     */
    @Nullable
    public TaskProgress getCalculationProgress(String orgId, String authPrjId) {
        String currentCalcId = getCurrentCalcId(orgId, authPrjId);
        if (currentCalcId == null) {
            return null;
        }

        String taskId = buildTaskId(orgId, authPrjId, currentCalcId);
        return taskProgressStorage.get(AuthPrjCalcTaskLifecycle.TASK_TYPE, taskId).orElse(null);
    }

    /**
     * 检查项目是否正在计算中
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @return 是否正在计算中
     */
    public boolean isCalculating(String orgId, String authPrjId) {
        TaskProgress progress = getCalculationProgress(orgId, authPrjId);
        if (progress == null) {
            return false;
        }
        
        TaskState currentState = progress.getState(stateFactory);
        return STATE_CALCULATING.equals(currentState.getCode());
    }
    
    /**
     * 构建任务ID
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @return 任务ID
     */
    private String buildTaskId(String orgId, String authPrjId, String traceId) {
        return orgId + ":" + authPrjId + ":" + traceId;
    }
}
