package com.yxt.talent.rv.application.activity.expt;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Lists;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.ExcelUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.dto.PerfUserImportDTO;
import com.yxt.talent.rv.application.activity.dto.PeriodExportResultDTO;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * 导入绩效的失败文件导出策略
 */
@Component
@RequiredArgsConstructor
public class PerfUserImportErrorExportStrategy extends AbstractExportStrategy {
    private static final String TASK_NAME = "apis.sptalentrv.perf.template.export.file.name";

    private static final String REMARK = "apis.sptalentrv.perf.template.export.file.remark";
    private static final String USER_NAME = "apis.sptalentrv.perf.template.export.file.header.username";
    private static final String FULL_NAME = "apis.sptalentrv.perf.template.export.file.header.fullname";
    private static final String PREF_LEVEL = "apis.sptalentrv.perf.template.export.file.header.preflevel";
    private static final String PERF_SCORE = "apis.sptalentrv.perf.template.export.file.header.perfsscore";

    private static final String ERROR_MSG = "apis.sptalentrv.perf.template.export.file.header.errormsg";

    private final I18nComponent i18nComponent;

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        List<PerfUserImportDTO> periodResult = (List<PerfUserImportDTO>) data;
        exportWithDynamicHeader(periodResult,  filePath);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = i18nComponent.getI18nValue(TASK_NAME);
        return buildDownInfo(userCache, fileName, taskName);
    }

    public void exportWithDynamicHeader(List<PerfUserImportDTO> data, String filePath) throws
        IOException {
        List<String> periods = data.get(0).getPeriodNames();
        File file = new File(filePath);
        boolean fileExist = file.exists();
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                file.getCanonicalFile().getParentFile().mkdirs();
            }

            fileExist = file.createNewFile();
        }
        if (!fileExist) {
            throw new FileNotFoundException("file not exist!");
        } else {
            ExcelWriterBuilder writerBuilder = EasyExcel.write(new FileOutputStream(file));
            ExcelWriter excelWriter = writerBuilder.build();

            WriteSheet sheet = EasyExcel.writerSheet("Sheet1").build();

            // 备注信息
            List<List<Object>> remarkData = new ArrayList<>();
            List<Object> remarkList = new ArrayList<>();
            remarkList.add(i18nComponent.getI18nValue(REMARK));
            remarkData.add(remarkList);

            //1、设置备注信息
            /**
             * 1.1、 合并单元格 【四个参数】
             * 参数1：合并开始的第一行     【0：表示第一行】
             * 参数2：和平结束的最后一行   【0：表示第一行，0-0=0，表示没有合并行】
             * 参数3：合并开始的第一列     【0：表示第一列】
             * 参数4：合并开始的最后一列   【size-1：表示合并的列数与数据表格的列数一致】
             */
            OnceAbsoluteMergeStrategy
                remarkMergeStrategy = new OnceAbsoluteMergeStrategy(0, 0, 0, periods.size() *  2 + 1);
            //1.2、设置内容居中
            WriteCellStyle contentStyle = new WriteCellStyle();
            //垂直居中
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            WriteFont writeFont = new WriteFont();
            //字体大小为16
            writeFont.setFontHeightInPoints((short) 10);
            writeFont.setColor(IndexedColors.RED.index);
            contentStyle.setWriteFont(writeFont);
            // 单元格策略 参数1为头样式【不需要头部，设置为null】，参数2位表格内容样式
            HorizontalCellStyleStrategy remarkHorizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentStyle);

            //3、设置数据表格的样式
            //  ---------- 头部样式 ----------
            WriteCellStyle headStyle = new WriteCellStyle();
            // 字体样式
            WriteFont headFont = new WriteFont();
            headFont.setFontHeightInPoints((short) 9);
            headFont.setBold(true);
            headStyle.setWriteFont(headFont);
            // 背景颜色
            headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            headStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.index);

            //  ---------- 内容样式 ----------
            WriteCellStyle bodyStyle = new WriteCellStyle();
            // 字体样式
            WriteFont bodyFont = new WriteFont();
            bodyFont.setFontHeightInPoints((short) 10);
            bodyStyle.setWriteFont(bodyFont);
            // 设置边框
            // bodyStyle.setBorderTop(BorderStyle.DOUBLE);
            bodyStyle.setBorderLeft(BorderStyle.THIN);
            bodyStyle.setBorderRight(BorderStyle.THIN);
            bodyStyle.setBorderBottom(BorderStyle.THIN);
            // 创建策略
            HorizontalCellStyleStrategy dataTableStrategy = new HorizontalCellStyleStrategy(headStyle, bodyStyle);

            /**
             * (4)、统一设置行高
             */
            // 设置表头行高【最上面的标题】  参数1：表头行高为0【不需要表头】    参数2：内容行高为28
            SimpleRowHeightStyleStrategy
                rowHeightStrategy1 = new SimpleRowHeightStyleStrategy((short) 0, (short) 28);
            // 设置数据表格的行高   null表示使用原来的行高
            SimpleRowHeightStyleStrategy rowHeightStrategy3 = new SimpleRowHeightStyleStrategy( null, (short) 18);


            // 生成表格1 ----页面中最上方的大标题
            WriteTable topicTable = EasyExcel.writerTable(0).registerWriteHandler(rowHeightStrategy1).registerWriteHandler(remarkMergeStrategy).registerWriteHandler(remarkHorizontalCellStyleStrategy).needHead(false).build();

            /**填写备注信息 */
            excelWriter.write(remarkData, sheet, topicTable);

            List<List<String>> headTitle1 =
                concatDynamicHead(
                    Lists.newArrayList(i18nComponent.getI18nValue(USER_NAME), i18nComponent.getI18nValue(FULL_NAME)), null);
            List<List<String>> headList = new ArrayList<>(headTitle1);

            List<List<String>> headTitle2 =
                concatDynamicHead(periods, Lists.newArrayList(i18nComponent.getI18nValue(PREF_LEVEL), i18nComponent.getI18nValue(PERF_SCORE)));
            headList.addAll(headTitle2);

            List<List<String>> headTitle3 =
                concatDynamicHead(
                    Lists.newArrayList(i18nComponent.getI18nValue(ERROR_MSG)),null);
            headList.addAll(headTitle3);

            WriteTable dataTable =
                EasyExcel.writerTable(1).registerWriteHandler(rowHeightStrategy3).registerWriteHandler(dataTableStrategy).head(headList).needHead(Boolean.TRUE).build();
            /** 填写表头 */

            List<List<Object>> datas = new ArrayList<>();
            for (PerfUserImportDTO info : data) {
                List<Object> userData = new ArrayList<>();
                userData.add(info.getUserName());
                userData.add(info.getFullName());
                for (String s : info.getPeriodNames()){
                    if (info.getLevelMap() != null){
                        Map<String, String> levelMap = info.getLevelMap();
                        userData.add(Optional.ofNullable(levelMap.get(s)).orElse(""));
                    }
                    if (info.getScoreMap() != null){
                        Map<String, String> scoreMap = info.getScoreMap();
                        userData.add(Optional.ofNullable(scoreMap.get(s)).orElse(""));
                    }
                }
                userData.add(info.getErrorMsg());
                datas.add(userData);
            }
            excelWriter.write(datas, sheet, dataTable);
            excelWriter.finish();
        }
    }

    /**
     * 组装动态表头
     *
     * @param titleName 标题List
     * @param secondaryHeadTitleList 次标题List
     * @return java.util.List<java.lang.String>
     */
    public List<List<String>> concatDynamicHead(
        List<String> titleName, List<String> secondaryHeadTitleList) {
        List<List<String>> headTtileList = new ArrayList<>();
        if (CollectionUtils.isEmpty(titleName)) {
            return headTtileList;
        }
        titleName.forEach(
            t -> {
                if (CollectionUtils.isEmpty(secondaryHeadTitleList)) {
                    List<String> headTitle = new ArrayList<>();
                    headTitle.add(t);
                    headTtileList.add(headTitle);
                    return;
                }
                secondaryHeadTitleList.forEach(
                    x -> {
                        List<String> headTitle = new ArrayList<>();
                        headTitle.add(t);
                        headTitle.add(x);
                        headTtileList.add(headTitle);
                    });
            });
        return headTtileList;
    }
}
