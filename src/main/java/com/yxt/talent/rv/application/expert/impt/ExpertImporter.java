package com.yxt.talent.rv.application.expert.impt;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.google.common.collect.Lists;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermission;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermissionResponse;
import com.yxt.enums.DeleteEnum;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.expert.ExpertImportDTO;
import com.yxt.talent.rv.application.expert.ExpertImportResultVO;
import com.yxt.talent.rv.application.expert.expt.ExpertImportErrorExportStrategy;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.EnableEnum;
import com.yxt.talent.rv.infrastructure.common.transfer.FileProcessedResult;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertDeptPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptExtPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImportSupport;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileReader;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@Component
@RequiredArgsConstructor
public class ExpertImporter extends
    FileImporter<ExpertImportDTO, ExpertImporter.ExpertImportProcessedResult, ExpertImportResultVO> {
    private static final String ERR_USERNAME_EMPTY = "apis.sptalentrv.perf.import.error.username";
    private static final String ERR_USERNAME_NOT_EXISTED =
        "apis.sptalentrv.perf.import.error.notexite";
    private static final String ERR_USERNAME_EXISTS =
        "apis.sptalentrv.expert.import.error.exists";
    private static final String ERR_USERNAME_DELETED =
        "apis.sptalentrv.perf.import.error.userdelete";
    private static final String ERR_USERNAME_REPEAT =
        "apis.sptalentrv.expert.import.error.repeat";

    private static final String ERR_USERNAME_DISABLED =
        "apis.sptalentrv.perf.import.error.userforbidden";

    private static final String ERR_IND_EMPTY =
        "apis.sptalentrv.expert.import.error.indempty";

    private static final String ERR_IND_OVERSIZE =
        "apis.sptalentrv.expert.import.error.indoversize";

    private static final String ERR_IND_NOTEXISTS =
        "apis.sptalentrv.expert.import.error.indnotexists";

    private static final String ERR_DEPT_OVERSIZE =
        "apis.sptalentrv.expert.import.error.deptoversize";

    private static final String ERR_DEPT_NOTEXISTS =
        "apis.sptalentrv.expert.import.error.deptnotexixts";

    private static final String ERR_USERNAME_NOPERMISSION = "apis.sptalentrv.expert.import.error.nopermission";

    private static final String EXPERT_ERROR_EXPORT_FILE = "apis.sptalentrv.expert.import.error.tmp.export.task.name";
    private static final String IMPORT_HEAD = "import:";
    private final ExpertImportErrorExportStrategy expertImportErrorExportStrategy;
    private final I18nComponent i18nComponent;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final ExpertMapper expertMapper;
    private final CoreAclService coreAclService;
    private final SpsdAclService spsdAclService;
    private final UdpDeptMapper deptMapper;
    private final ExpertDeptMapper expertDeptMapper;
    private final ExpertIndicatorMapper expertIndicatorMapper;

    @Nullable
    public ExpertImportResultVO toImport(
        FileImportCmd bean, MultipartFile file, UserCacheDetail userCache) {

        String orgId = userCache.getOrgId();
        String operator = userCache.getUserId();
        String lockKey = String.format(RedisKeys.LK_EXPERT_IMPT, IMPORT_HEAD, orgId, operator);
        String errorFileName = i18nComponent.getI18nValue(EXPERT_ERROR_EXPORT_FILE) +
                               DateTimeUtil.dateToString(
                                   new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) +
                               FileConstants.FILE_SUFFIX_XLSX;

        Function<List<ExpertImportDTO>, ExpertImportProcessedResult> dataProcessor =
            importDataList -> dataProcess(importDataList, orgId, operator);

        FileImportSupport<ExpertImportDTO, ExpertImportProcessedResult, ExpertImportResultVO>
            fileImportSupport =
            FileImportSupport.<ExpertImportDTO, ExpertImportProcessedResult, ExpertImportResultVO>builder()
                .file(file)
                .fileId(bean.getFileId())
                .tranId(lockKey)
                .startRow(2)
                .orgId(orgId)
                .operator(operator)
                .importContentClazz(ExpertImportDTO.class)
                .dataReader(new CliExpertFileReader(file, bean.getFileId()))
                .dataProcessor(dataProcessor)
                .outputStrategy(expertImportErrorExportStrategy)
                .errorFileName(errorFileName)
                .resultProcessor(this::generateImportResult)
                .build();
        return toImport(fileImportSupport);
    }

    private ExpertImportResultVO generateImportResult(ExpertImportProcessedResult processedResult) {
        int repeatCount = processedResult.getRepeatCount();
        int failedCount = processedResult.getFailedCount();
        int successCount = processedResult.getSuccessCount();
        int totalCount = processedResult.getTotalCount();
        List<ExpertImportDTO> failedData = processedResult.getFailedData();
        Map<String, List<ExpertImportDTO>> result = processedResult.getResult();
        String errorFilePath =
            Optional.ofNullable(processedResult.getErrorFilePath()).orElse(EMPTY);

        return ExpertImportResultVO.childBuilder()
            .repeatCount(repeatCount)
            .failedCount(failedCount)
            .successCount(successCount)
            .totalCount(totalCount)
            .filePath(errorFilePath)
            .successData(result)
            .failData(failedData)
            .build();
    }

    @NotNull
    private ExpertImportProcessedResult dataProcess(
        List<ExpertImportDTO> importDataList, String orgId, String userId) {
        // 校验导入的账号信息
        // 检查账号列或所属关卡列是否都空
        List<ExpertImportDTO> userList =
            StreamUtil.filterList(importDataList, b -> StringUtils.isNotBlank(b.getUsername()));
        if (CollectionUtils.isEmpty(userList)) {
            throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_NO_USERNAME);
        }

        // 获取导入用户名信息
        List<UdpLiteUserPO> importUsers = getImportUsers(userList, orgId);
        log.debug("LOG10090:{}", importUsers.size());
        Map<String, UdpLiteUserPO> importUserMaps =
            StreamUtil.list2map(importUsers, UdpLiteUserPO::getUsername);
        List<ExpertPO> expertPOS = expertMapper.findByOrgId(orgId);
        List<String> existsUserIds = expertPOS.stream().map(ExpertPO::getUserId).collect(Collectors.toList());

        List<String> accessUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(importUsers)) {
            UserDataPermission userDataPermission = new UserDataPermission();
            userDataPermission.setUserId(userId);
            userDataPermission.setOrgId(orgId);
            userDataPermission.setDataPermsCode("sp_expert_center_user_extent");
            userDataPermission.setProductCode("xxv2");
            userDataPermission.setNavCode("sp_gwnl_expert_center");
            List<List<String>> splitList = Lists.partition(importUsers.stream().map(UdpLiteUserPO::getId).collect(
                Collectors.toList()), 300);
            for (List<String> strings : splitList) {
                userDataPermission.setVerifyUsers(strings);
                UserDataPermissionResponse dataScopeResp = coreAclService.verifyUserDataPermission(userDataPermission);
                if (null != dataScopeResp && CollectionUtils.isNotEmpty(dataScopeResp.getUserIds())) {
                    accessUserIds.addAll(dataScopeResp.getUserIds());
                }
            }
        }

        // 导入重复数
        int repeatCount = 0;
        List<ExpertPO> iptExpertPOS = new ArrayList<>();
        List<ExpertDeptPO> expertDeptPOS = new ArrayList<>();
        List<ExpertIndicatorPO> expertIndicatorPOS = new ArrayList<>();
        List<String> importIds = new ArrayList<>();
        for (ExpertImportDTO expertImportDTO : importDataList) {
            String importUserId = checkUserName(expertImportDTO, importUserMaps, existsUserIds, importIds, accessUserIds);
            List<String> indicatorIds = checkIndicatorList(orgId, expertImportDTO);
            List<String> deptIds = checkDeptList(orgId, expertImportDTO);

            if (StringUtils.isNotBlank(expertImportDTO.getErrorMsg())) {
                // 数据异常，本条数据为异常数据不导入，跳出，执行下一条
                expertImportDTO.setErrorMsg(expertImportDTO.getErrorMsg().substring(1));
                continue;
            }

            ExpertPO expertPO = new ExpertPO();
            expertPO.setId(ApiUtil.getUuid());
            expertPO.setOrgId(orgId);
            expertPO.setRemark("");
            expertPO.setUserId(importUserId);
            expertPO.setDeleted(YesOrNo.NO.getValue());
            expertPO.setCreateUserId(userId);
            expertPO.setUpdateUserId(userId);
            expertPO.setCreateTime(LocalDateTime.now());
            expertPO.setUpdateTime(LocalDateTime.now());
            iptExpertPOS.add(expertPO);

            if (CollectionUtils.isNotEmpty(deptIds)) {
                for (String deptId : deptIds) {
                    ExpertDeptPO expertDeptPO = new ExpertDeptPO();
                    expertDeptPO.setExpertId(expertPO.getId());
                    expertDeptPO.setOrgId(orgId);
                    expertDeptPO.setDeleted(YesOrNo.NO.getValue());
                    expertDeptPO.setId(ApiUtil.getUuid());
                    expertDeptPO.setDeptId(deptId);
                    expertDeptPO.setCreateUserId(userId);
                    expertDeptPO.setUpdateUserId(userId);
                    expertDeptPO.setCreateTime(LocalDateTime.now());
                    expertDeptPO.setUpdateTime(LocalDateTime.now());
                    expertDeptPOS.add(expertDeptPO);
                }
            }

            if (CollectionUtils.isNotEmpty(indicatorIds)) {
                for (String inds : indicatorIds) {
                    ExpertIndicatorPO expertIndicatorPO = new ExpertIndicatorPO();
                    expertIndicatorPO.setExpertId(expertPO.getId());
                    expertIndicatorPO.setOrgId(orgId);
                    expertIndicatorPO.setDeleted(YesOrNo.NO.getValue());
                    expertIndicatorPO.setId(ApiUtil.getUuid());
                    expertIndicatorPO.setIndId(inds);
                    expertIndicatorPO.setCreateUserId(userId);
                    expertIndicatorPO.setUpdateUserId(userId);
                    expertIndicatorPO.setCreateTime(LocalDateTime.now());
                    expertIndicatorPO.setUpdateTime(LocalDateTime.now());
                    expertIndicatorPOS.add(expertIndicatorPO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(iptExpertPOS)) {
            expertMapper.batchInsertOrUpdate(iptExpertPOS);
        }

        if (CollectionUtils.isNotEmpty(expertDeptPOS)) {
            expertDeptMapper.batchInsertOrUpdate(expertDeptPOS);
        }

        if (CollectionUtils.isNotEmpty(expertIndicatorPOS)) {
            expertIndicatorMapper.batchInsertOrUpdate(expertIndicatorPOS);
        }

        return ExpertImportProcessedResult.builder() // NOSONAR
            .repeatCount(repeatCount)
//            .result(userResult)
            .totalData(importDataList)
            .failedData(StreamUtil.filterList(
                importDataList,
                b -> StringUtils.isNotBlank(b.getErrorMsg())))
            .successData(StreamUtil.filterList(
                importDataList,
                b -> StringUtils.isBlank(b.getErrorMsg())))
            .build();
    }

    private List<String> checkDeptList(String orgId, ExpertImportDTO expertImportDTO) {
        if (StringUtils.isBlank(expertImportDTO.getDeptList())) {
            return new ArrayList<>();
        }
        List<String> deptNameList = Arrays.asList(expertImportDTO.getDeptList().split("，"));
        deptNameList = deptNameList.stream().distinct().collect(Collectors.toList());
        if (deptNameList.size() > 20) {
            expertImportDTO.setErrorMsg(
                expertImportDTO.getErrorMsg() + "，" + i18nComponent.getI18nValue(ERR_DEPT_OVERSIZE));
        }

        List<String> deptIds = new ArrayList<>();
        
        Map<String, String> udpDeptMap = new HashMap<>();
        List<UdpDeptExtPO> udpDeptExtPOS = deptMapper.selectExtByOrgIdAndNames(orgId, deptNameList);
        if (CollectionUtils.isNotEmpty(udpDeptExtPOS)) {
            udpDeptMap = StreamUtil.list2map(udpDeptExtPOS, UdpDeptExtPO::getRoutingPathName, UdpDeptExtPO::getId);
        }

        for (String string : deptNameList) {
            if (!udpDeptMap.containsKey(string)) {
                expertImportDTO.setErrorMsg(
                    expertImportDTO.getErrorMsg() + "，" + String.format(i18nComponent.getI18nValue(ERR_DEPT_NOTEXISTS), string));
                continue;
            }

            deptIds.add(udpDeptMap.get(string));
        }


        return deptIds;
    }

    private List<String> checkIndicatorList(String orgId, ExpertImportDTO expertImportDTO) {
        if (StringUtils.isBlank(expertImportDTO.getIndicatorList())) {
            expertImportDTO.setErrorMsg(expertImportDTO.getErrorMsg() + "，" + i18nComponent.getI18nValue(ERR_IND_EMPTY));
            return new ArrayList<>();
        }

        List<String> indicatorNums = Arrays.asList(expertImportDTO.getIndicatorList().split("，"));
        indicatorNums = indicatorNums.stream().distinct().collect(Collectors.toList());
        if (indicatorNums.size() > 200) {
            expertImportDTO.setErrorMsg(expertImportDTO.getErrorMsg() + "，" + i18nComponent.getI18nValue(ERR_IND_OVERSIZE));
        }

        Map<String, String> numIdMap = spsdAclService.getIndIdsByIndNums(orgId, indicatorNums);
        for (String indicatorNum : indicatorNums) {
            if (!numIdMap.containsKey(indicatorNum)) {
                expertImportDTO.setErrorMsg(expertImportDTO.getErrorMsg() + "，" + String.format(i18nComponent.getI18nValue(ERR_IND_NOTEXISTS), indicatorNum));
            }
        }


        return new ArrayList<>(numIdMap.values());
    }


    /**
     * 用户账户check
     */
    private String checkUserName(
        ExpertImportDTO importDTO, Map<String, UdpLiteUserPO> importUserMaps, List<String> existsIds, List<String> importIds, List<String> accessUserIds) {
        // 不填
        if (StringUtils.isBlank(importDTO.getUsername())) {
            importDTO.setErrorMsg(importDTO.getErrorMsg() + "，" + i18nComponent.getI18nValue(ERR_USERNAME_EMPTY));
            return "";
        }
        // 用户信息
        UdpLiteUserPO user = importUserMaps.get(importDTO.getUsername());
        // 不存在
        if (user == null || StringUtils.isBlank(user.getId())) {
            importDTO.setErrorMsg(importDTO.getErrorMsg() + "，" + i18nComponent.getI18nValue(ERR_USERNAME_NOT_EXISTED));
            return "";
        }
        // 删除
        if (user.getDeleted() == DeleteEnum.DELETED.getCode()) {
            importDTO.setErrorMsg(importDTO.getErrorMsg() + "，" + i18nComponent.getI18nValue(ERR_USERNAME_DELETED));
            return user.getId();
        }

        String errorMsg = "";
        if (user.getStatus() == EnableEnum.DISABLED.getCode()) {
            errorMsg += "，" + i18nComponent.getI18nValue(ERR_USERNAME_DISABLED);
        }

        if (existsIds.contains(user.getId())) {
            errorMsg += "，" + i18nComponent.getI18nValue(ERR_USERNAME_EXISTS);
        }

        if (importIds.contains(user.getId())) {
            errorMsg += "，" + i18nComponent.getI18nValue(ERR_USERNAME_REPEAT);
        }

        if (!accessUserIds.contains(user.getId())) {
            errorMsg += "，" + i18nComponent.getI18nValue(ERR_USERNAME_NOPERMISSION);
        }

        if (StringUtils.isNotBlank(errorMsg)) {
            importDTO.setErrorMsg(errorMsg);
        }
        importIds.add(user.getId());

        return user.getId();
    }

    private List<UdpLiteUserPO> getImportUsers(
        List<ExpertImportDTO> importDtos, String orgId) {
        Set<String> importUsernames = new HashSet<>(importDtos.size());
        importDtos.forEach(
            performance4Import -> importUsernames.add(performance4Import.getUsername()));
        return udpLiteUserMapper.selectByUserNamesIncludeDeleted(orgId, importUsernames);
    }

    @Setter
    @Getter
    @ToString
    @SuperBuilder
    static class ExpertImportProcessedResult extends FileProcessedResult<ExpertImportDTO> {
        public final int repeatCount;
        public final Map<String, List<ExpertImportDTO>> result;
    }

    @RequiredArgsConstructor
    private class CliExpertFileReader extends FileReader<ExpertImportDTO> {

        private final MultipartFile file;

        private final String fileId;

        @Override
        @jakarta.annotation.Nonnull
        public List<ExpertImportDTO> read() {
            return doReadExcel();
        }

        /**
         * 由于导入文件中有部分表头是动态生成的, 所以这里没办法使用静态导入的方式, 而是采用动态解析的方式来处理导入的数据
         */
        @jakarta.annotation.Nonnull
        private List<ExpertImportDTO> doReadExcel() {
            ExcelReader excelReader = null;
            List<ExpertImportDTO> importDataList;
            try (
                InputStream inputStream = this.getInputStream(file, fileId)
            ) {
                excelReader = EasyExcelFactory.read(inputStream).build();
                ReadSheet sheet = excelReader.excelExecutor().sheetList().get(0);

                // 读取sheet
                EasyExcelListener listener = new EasyExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(1);
                excelReader.read(sheet);

                // 提取Excel数据
                importDataList = generateExpert4ExcelImportList(listener);
            } catch (IOException e) {
                throw new ApiException(e.getMessage());
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
            }
            return importDataList;
        }


        @jakarta.annotation.Nonnull
        private List<ExpertImportDTO> generateExpert4ExcelImportList(
            EasyExcelListener listener) {

            List<Map<Integer, String>> list = listener.getData();
            List<ExpertImportDTO> muList = new ArrayList<>(list.size());

            // 表头
            Map<Integer, String> headMap = list.get(0);
            int allcellSize = headMap.size();
            if (headMap.isEmpty() || headMap.size() < 4) {
                throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR);
            }
            List<String> headPeriodNames = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                if (StringUtils.isNotBlank(headMap.get(i))) {
                    headPeriodNames.add(headMap.get(i));
                }
            }

            if (CollectionUtils.isEmpty(headPeriodNames)) {
                throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR);
            }

            try {
                // 内容
                for (int j = 1; j < list.size(); j++) {
                    Map<Integer, String> map = list.get(j);
                    int i = 0;
                    ExpertImportDTO temp = new ExpertImportDTO();
                    temp.setUsername(map.get(i++));
                    temp.setFullName(map.get(i++));
                    temp.setIndicatorList(map.get(i++));
                    temp.setDeptList(map.get(i));

                    if (StringUtils.isBlank(temp.getUsername()) && StringUtils.isBlank(temp.getFullName()) &&
                        StringUtils.isBlank(temp.getIndicatorList()) && StringUtils.isBlank(temp.getDeptList())) {
                        continue;
                    }
                    muList.add(temp);
                }
            } catch (Exception e) {
                throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR);
            }
            return muList;
        }
    }
}
