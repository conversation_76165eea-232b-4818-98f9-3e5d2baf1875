package com.yxt.talent.rv.application.activity.slot;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.bean.RepeatReq;
import com.yxt.aom.activity.custom.SingleActivityCompo;
import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.AssessmentActivityResult;
import com.yxt.aom.datamodel.activityresult.BaseActivityResult;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.aom.datamodel.event.ScoResultEvent;
import com.yxt.aom.datamodel.scoresult.BaseScoResult;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.activity.dto.UserIndicatorPushDataDTO;
import com.yxt.talent.rv.application.democopy.DemoTableProvider;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_MODEL_ID;

@Slf4j
@RequiredArgsConstructor
@Component("singleActivityCompo4ActvPerf")
public class SingleActivityCompo4ActvPerf implements SingleActivityCompo {

    private final DemoTableProvider demoTableProvider;
    private final SpsdAclServiceImpl spsdAclService;

    @Override
    public <T extends BaseActivityResult> void resultRollUpCallBack(
        Actor actor, TargetObject targetObject, ActivityArrangeItem item, String traceId, T result) {

    }

    @Override
    public void activityRepeat(com.yxt.aom.base.entity.control.BaseActivityResult baseActivityResult, RepeatReq req) {

    }

    @Override
    public void scoResultCallBack(ScoResultEvent<BaseScoResult> event, String messageBody) {

    }

    @Override
    public void updateIdFields4DemoCopy(ResultCopyReq bean, List<AssessmentActivityResult> oldAssessmentResults,
            List<ActivityObjectiveResult> oldObjectiveResults) {
        try {

            Map<String, Object> logmap = new LinkedHashMap<>(BeanHelper.beanToMap(bean));
            logmap.remove("userMap");
            log.debug("LOG22003:{}", BeanHelper.bean2Json(logmap, JsonInclude.Include.ALWAYS));
            log.debug("LOG22013:{}", BeanHelper.bean2Json(oldAssessmentResults, JsonInclude.Include.ALWAYS));
            log.debug("LOG22023:{}", BeanHelper.bean2Json(oldObjectiveResults, JsonInclude.Include.ALWAYS));

            OrgInit4Mq orgInit = new OrgInit4Mq();
            orgInit.setTargetOrgId(bean.getTgtOrgId());
            orgInit.setSourceOrgId(bean.getSrcOrgId());
            DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
            runner.addPreSetIdMap(SPSD_MODEL_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_MODEL_ID));
            runner.addPreSetIdMap(SPSD_INDICATOR_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_INDICATOR_ID));

            //绩效 ext  UserIndicatorPushDataDTO
            if (CollectionUtils.isNotEmpty(oldAssessmentResults)) {
                for (AssessmentActivityResult item : oldAssessmentResults) {
                    String perfExt = item.getExt();
                    if (StringUtils.isBlank(perfExt)) {
                        continue;
                    }
                    UserIndicatorPushDataDTO jsonData = JSON.parseObject(perfExt, UserIndicatorPushDataDTO.class);
                    if (Objects.isNull(jsonData)) {
                        continue;
                    }
                    resetExtInfo(runner, jsonData);
                    item.setExt(JSON.toJSONString(jsonData));
                }

            }
            if (CollectionUtils.isNotEmpty(oldObjectiveResults)) {
                for (ActivityObjectiveResult item : oldObjectiveResults) {
                    if (StringUtils.isNotBlank(item.getObjectiveModeId())) {
                        String objectiveModeId = transferNewId(runner, SPSD_MODEL_ID, item.getObjectiveModeId());
                        log.debug("LOG22143:oldId={},newId={}", item.getObjectiveModeId(), objectiveModeId);
                        item.setObjectiveModeId(objectiveModeId);
                    }
                    if (StringUtils.isNotBlank(item.getObjectiveId())) {
                        String objectiveId = transferNewId(runner, SPSD_INDICATOR_ID, item.getObjectiveId());
                        log.debug("LOG22153:oldId={},newId={}", item.getObjectiveId(), objectiveId);
                        item.setObjectiveId(objectiveId);
                    }
                    String perfExt = item.getExt();
                    if (StringUtils.isBlank(perfExt)) {
                        continue;
                    }
                    UserIndicatorPushDataDTO jsonData = JSON.parseObject(perfExt, UserIndicatorPushDataDTO.class);
                    if (Objects.isNull(jsonData)) {
                        continue;
                    }
                    resetExtInfo(runner, jsonData);
                    item.setExt(JSON.toJSONString(jsonData));
                }
            }

            log.debug("LOG22093:{}", BeanHelper.bean2Json(oldAssessmentResults, JsonInclude.Include.ALWAYS));
            log.debug("LOG22103:{}", BeanHelper.bean2Json(oldObjectiveResults, JsonInclude.Include.ALWAYS));
        } catch(Exception e) {
            log.error("LOG22243:", e);
            throw e;
        }
    }

    private void resetExtInfo(DemoCopyRunner runner, UserIndicatorPushDataDTO jsonData) {
        if (StringUtils.isNotBlank(jsonData.getResultConfId())) {
            String resultConfId = transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_RESULT_CONF_ID, jsonData.getResultConfId());
            log.debug("LOG22113:oldId={},newId={}", jsonData.getResultConfId(), resultConfId);
            jsonData.setResultConfId(resultConfId);
        }
        List<UserIndicatorPushDataDTO.UserPeriodResult> userPeriodResults = jsonData.getUserPeriodResults();
        if (CollectionUtils.isNotEmpty(userPeriodResults)) {
            for (UserIndicatorPushDataDTO.UserPeriodResult userPeriodResult : userPeriodResults) {
                String periodId = transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID, userPeriodResult.getPeriodId());
                log.debug("LOG22123:oldId={},newId={}", userPeriodResult.getPeriodId(), periodId);
                userPeriodResult.setPeriodId(periodId);
                String gradeId = transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_PERF_GRADE_ID, userPeriodResult.getGradeId());
                log.debug("LOG22133:oldId={},newId={}", userPeriodResult.getGradeId(), gradeId);
                userPeriodResult.setGradeId(gradeId);
            }
            jsonData.setUserPeriodResults(userPeriodResults);
        }

    }

    private String transferNewId(DemoCopyRunner runner, String idMap, String sourceId) {
        if (StringUtils.isBlank(idMap) || StringUtils.isBlank(sourceId)) {
            return sourceId;
        }
        String newId = runner.queryIdMapValue(idMap, sourceId);
        if (StringUtils.isBlank(newId)) {
            return sourceId;
        }
        return newId;
    }
}
