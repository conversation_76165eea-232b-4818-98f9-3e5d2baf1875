package com.yxt.talent.rv.application.xpd.rule;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vip.vjtools.vjkit.collection.ListUtil;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.*;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.bean.SpRuleColumnBean;
import com.yxt.spsdk.common.bean.SpRuleGroupBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.xpd.actvimpt.XpdActvImptService;
import com.yxt.talent.rv.application.xpd.common.XpdPOFactory;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.ShowTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.XpdImportTypeEnum;
import com.yxt.talent.rv.application.xpd.grid.XpdGridReplicator;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.*;
import com.yxt.talent.rv.controller.manage.xpd.rule.viewobj.XpdDim4Get;
import com.yxt.talent.rv.controller.manage.xpd.rule.viewobj.XpdDimVo;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.ModelDimDTO;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 新盘点-项目规则配置
 *
 * <AUTHOR>
 * @date 2024/12/6 11:23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class XpdRuleConfAppService extends XpdRuleBaseService<XpdRuleConfAppService> {

    private final XpdGridMapper xpdGridMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdGridDimCombMapper xpdGridDimCombMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdGridRatioMapper xpdGridRatioMapper;

    private final XpdGridReplicator xpdGridReplicator;
    private final RuleConfComponent ruleConfComponent;
    private final XpdRuleValidator xpdRuleValidator;
    private final XpdDimRuleValidator xpdDimRuleValidator;

    private final SpsdAclService spsdAclService;
    private final ILock lockService;
    private final XpdService xpdService;
    private final XpdActvImptService xpdActvImptService;

    private final SptalentsdFacade sptalentsdFacade;
    private final I18nTranslator i18nTranslator;
    private final AuthService authService;
    private final SpRuleService spRuleService;
    private final XpdDimMapper xpdDimMapper;
    private final XpdMapper xpdMapper;

    /**
     * 获取全局匹配规则
     *
     * @param orgId 机构ID
     * @param xpdId 新盘点项目ID
     */
    public XpdRuleConfInfoOutDto getXpdRuleConfInfo(String orgId, String xpdId) {
        XpdRuleConfInfoOutDto.XpdRuleConfInfoOutDtoBuilder confInfoOutBuilder = XpdRuleConfInfoOutDto.builder();

        XpdPO xpd = checkXpdExist(xpdId);
        XpdRuleConfPO ruleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        if (ruleConf == null) {
            return confInfoOutBuilder.build();
        }
        confInfoOutBuilder.ruleConfId(ruleConf.getId())
                .gridId(ruleConf.getTemplateGridId())
                .resultType(ruleConf.getResultType())
                .scoreSystem(ruleConf.getScoreSystem())
                .ruleConfVersion(ruleConf.getVersion());

        String gridId = ruleConf.getTemplateGridId();
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        if (xpdGrid != null) {
            confInfoOutBuilder.gridName(xpdGrid.getGridName());
        }

        // 维度列表
        List<XpdDimPO> dimList = xpdDimMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isNotEmpty(dimList)) {
            // 人才标准的维度Ids，用来拿维度名称
            List<String> sdDimIds = StreamUtil.mapList(dimList, XpdDimPO::getSdDimId);
            List<ModelBase4Facade> sdDimList = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), sdDimIds);
            List<DimSimpleInfoDto> dimInfoDtoList = convert2DimInfoList(dimList, sdDimList);
            confInfoOutBuilder.dimList(dimInfoDtoList);
        }

        return confInfoOutBuilder.build();
    }

    /**
     * 获取快速配置规则
     *
     * @param orgId 机构ID
     * @param xpdId 项目ID
     * @return XpdRuleConfFastDto
     */
    public XpdRuleConfFastDto getXpdRuleConfFastInfo(String orgId, String xpdId) {
        XpdRuleConfPO ruleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        if (Objects.isNull(ruleConf)) {
            return new XpdRuleConfFastDto();
        }

        XpdRuleConfFastDto ruleConfFastDto = new XpdRuleConfFastDto();
        // 快速生成规则的快照，json格式，要转化成VO格式
        String snapJson = ruleConf.getSnap();
        if (StringUtils.isNotEmpty(snapJson)) {
            try {
                ruleConfFastDto = JSON.parseObject(snapJson, XpdRuleConfFastDto.class);
            } catch (Exception e) {
                log.error("快速生成规则格式有误");
            }
        }
        ruleConfFastDto.setXpdId(xpdId);
        ruleConfFastDto.setRuleConfId(ruleConf.getId());
        ruleConfFastDto.setRuleConfVersion(ruleConf.getVersion());
        ruleConfFastDto.setResultType(ruleConf.getResultType());
        ruleConfFastDto.setScoreSystem(ruleConf.getScoreSystem());
        if (CollectionUtils.isNotEmpty(ruleConfFastDto.getLevelRuleList())) {
            Map<String, XpdDimLevelRuleDto> levelRuleMap = StreamUtil.list2map(ruleConfFastDto.getLevelRuleList(),
                    XpdDimLevelRuleDto::getGridLevelId);
            Map<String, XpdGridLevelPO> gridLevelMap = StreamUtil
                    .list2map(xpdGridLevelMapper.listByGridId(orgId, ruleConf.getGridId()), XpdGridLevelPO::getId);
            levelRuleMap.forEach((gridLevelId, level) -> {
                if (gridLevelMap.containsKey(gridLevelId)) {
                    level.setLevelName(gridLevelMap.get(gridLevelId).getLevelName());
                }
            });
        }

        return ruleConfFastDto;
    }

    /**
     * 获取盘点的详细规则
     *
     * @param orgId 机构ID
     * @param xpdId 盘点项目ID
     * @return 详细规则数据
     */
    public XpdRuleDetailInfoDto getXpdRuleDetailInfo(String orgId, String xpdId) {

        XpdPO xpd = checkXpdExist(xpdId);
        String modelId = xpd.getModelId();
        XpdRuleConfPO xpdRuleConf = checkXpdRuleConfExistByXpdId(orgId, xpdId);

        XpdRuleDetailInfoDto detailInfoDto = new XpdRuleDetailInfoDto();
        detailInfoDto.setXpdId(xpdId);
        detailInfoDto.setScoreSystem(xpdRuleConf.getScoreSystem());
        Activity activity = xpdService.findAomPrjByAomId(orgId, xpdId);
        if (activity != null) {
            detailInfoDto.setXpdName(activity.getActvName());
        }
        log.debug("LOG20243: xpdId={}, orgId={}", xpdId, orgId);

        // 项目的维度
        List<XpdDimPO> xpdDimList = xpdDimMapper.listByXpdId(orgId, xpdId);
        // 维度规则
        List<XpdDimRulePO> dimRuleList = xpdDimRuleMapper.listByXpdId(orgId, xpdId);

        // 1.校验项目规则
        XpdRulePO xpdRule = xpdRuleMapper.getByXpdId(orgId, xpd.getId());
        Map<String, BigDecimal> totalScoreMap = new LinkedHashMap<>();
        List<XpdIndicatorCalcBean> indicatorCalcList = Lists.newArrayList();
        if (xpdRule != null) {
            totalScoreMap = calcXpdTotalScoreAndDimScores(orgId, xpd, xpdRule, true, indicatorCalcList);
        }
        fillAndValidateXpdRule(orgId, xpd, xpdRule, xpdRuleConf, dimRuleList, totalScoreMap, detailInfoDto);

        List<String> sdDimIds = StreamUtil.mapList(xpdDimList, XpdDimPO::getSdDimId);
        if (CollectionUtils.isEmpty(sdDimIds)) {
            detailInfoDto.setDms(Lists.newArrayList());
            return detailInfoDto;
        }

        // 维度数据
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, modelId, sdDimIds);
        Map<String, ModelBase4Facade> dimInfoMap = StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId);

        // 维度的计算规则-指标
        Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcMap = xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId).stream()
                .collect(Collectors.groupingBy(XpdDimRuleCalcPO::getDimRuleId));
        // 模型下的所有指标
        Map<String, IndicatorDto> indicatorMap = StreamUtil.list2map(spsdAclService.getLastIndicators(orgId, modelId),
                IndicatorDto::getItemId);
        // 维度的分层规则
        Map<String, XpdGridLevelPO> gridLevelMap = StreamUtil.list2map(xpdGridLevelMapper.listByXpdId(orgId, xpdId),
                XpdGridLevelPO::getId);

        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRuleList);
        Map<String, List<XpdDimRulePO>> dimRuleChildrenMap = getDimRuleParentIdMap(dimRuleList);

        Map<String, XpdImportPO> importDimMap = StreamUtil.list2map(
                xpdImportMapper.findByXpdIdAndImportType(orgId, xpdId, XpdImportTypeEnum.DIM.getCode(), true),
                XpdImportPO::getSdDimId);
        // 2.填充一级节点:所有盘点维度
        List<XpdDimRuleDetailInfoDto> firstDims = Lists.newArrayList();
        for (String sdDimId : sdDimIds) {
            firstDims.add(fillAndCheckFirstDimRule(sdDimId, xpd, xpdRuleConf.getResultType(), dimInfoMap, dimRuleMap,
                    gridLevelMap, dimRuleCalcMap, importDimMap, totalScoreMap));
        }
        detailInfoDto.setDms(firstDims);
        // 3.填充二级及以下节点
        for (XpdDimRuleDetailInfoDto firstDim : firstDims) {
            if (StringUtils.isNotEmpty(firstDim.getDimRuleId())) {
                fillDeeperDimRule(firstDim, xpd, xpdRuleConf.getResultType(), dimInfoMap, dimRuleChildrenMap,
                        dimRuleCalcMap, indicatorMap, importDimMap, totalScoreMap);
            }
        }

        return detailInfoDto;
    }

    /**
     * 校验项目规则
     */
    private void fillAndValidateXpdRule(String orgId,
            XpdPO xpd,
            XpdRulePO xpdRule,
            XpdRuleConfPO xpdRuleConf,
            List<XpdDimRulePO> dimRuleList,
            Map<String, BigDecimal> totalScoreMap,
            XpdRuleDetailInfoDto detailInfo) {
        // 项目规则
        // 规则没配
        if (Objects.isNull(xpdRule)) {
            // 暂未配置计算规则
            detailInfo.setError(new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_RULE_CALC_EMPTY,
                    RULE_CALC_EMPTY));
        } else {
            String xpdRuleId = xpdRule.getId();
            detailInfo.setXpdRuleId(xpdRuleId);
            detailInfo.setCalcType(xpdRule.getCalcType());
            detailInfo.setLevelType(xpdRule.getLevelType());
            detailInfo.setResultType(xpdRule.getResultType());
            detailInfo.setRuleDesc(xpdRule.getRuleDesc());
            // 项目的分层
            List<XpdLevelPO> xpdLevels = getXpdLevelRules(orgId, xpdRuleId);
            detailInfo.setLevelList(convert2XpdLevelRules(xpdLevels, xpdRule.getResultType()));

            // 分层规则
            RuleMainBase mainData = new RuleMainBase();
            mainData.setOrgId(orgId);
            mainData.setBizId(xpd.getId());
            mainData.setLocale(YxtBasicUtils.requestLocale());
            spRuleService.calcRuleDisplay(mainData, detailInfo.getLevelList(),
                item -> Optional.ofNullable(item.getSpRuleBean()).orElse(item.getJudgeRule()), XpdLevelRuleDto::setFormulaDisplay);

            ErrorInfo xpdError;
            // 项目的计算方式：按指标结果计算
            if (XpdCalcTypeEnum.byIndicator(xpdRule.getCalcType())) {
                xpdError = xpdRuleValidator.validateByIndicator(orgId, xpd, xpdRule, totalScoreMap);
            } else {
                // 项目的计算方式：按维度结果计算
                xpdError = xpdRuleValidator.validateByDim(orgId, xpdRuleConf.getResultType(), xpdRule, dimRuleList,
                        totalScoreMap);
            }
            if (Objects.isNull(detailInfo.getError())) {
                detailInfo.setError(xpdError);
            }
        }
    }

    /**
     * 填充一级节点:所有盘点维度
     *
     * @param sdDimId        所有盘点维度IDs-标准
     * @param confResultType 全局规则配置中的结果类型
     * @param dimInfoMap     维度基本信息[from facade] key:维度ID-标准
     * @param dimRuleMap     维度规则
     * @param gridLevelMap   分层规则
     */
    private XpdDimRuleDetailInfoDto fillAndCheckFirstDimRule(String sdDimId,
            XpdPO xpd,
            Integer confResultType,
            Map<String, ModelBase4Facade> dimInfoMap,
            Map<String, XpdDimRulePO> dimRuleMap,
            Map<String, XpdGridLevelPO> gridLevelMap,
            Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcMap,
            Map<String, XpdImportPO> importDimMap,
            Map<String, BigDecimal> totalScoreMap) {

        XpdDimRuleDetailInfoDto dm = new XpdDimRuleDetailInfoDto();
        if (!dimInfoMap.containsKey(sdDimId)) {
            return dm;
        }

        ModelBase4Facade modelBase = dimInfoMap.get(sdDimId);
        dm.setXpdId(xpd.getId());
        dm.setDmId(sdDimId);
        dm.setRequireBaseId(modelBase.getId());
        dm.setDmName(modelBase.getDmName());
        dm.setEnabled(1);
        dm.setIsDirectDim(1);
        int dmType = DimTypeEnum.isPerfName(modelBase.getCustomName()) ? DimTypeEnum.PERF.getCode() : modelBase.getDmType();
        dm.setDimType(dmType);

        // 导入的维度
        if (importDimMap.containsKey(sdDimId)) {
            dm.setEnabled(0);
            return dm;
        }

        // 规则没配[暂未配置计算规则]
        if (!dimRuleMap.containsKey(sdDimId)) {
            dm.setError(new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_DIM_RULE_CALC_EMPTY,
                    RULE_CALC_EMPTY));
            return dm;
        }

        XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
        dm.setDimRuleId(dimRule.getId());
        dm.setCalcType(dimRule.getCalcType());
        dm.setLevelType(dimRule.getLevelType());
        dm.setResultType(dimRule.getResultType());
        dm.setEnabled(dimRule.getEnabled());
        dm.setRuleDesc(dimRule.getRuleDesc());
        List<XpdDimLevelRuleDto> dimLevelRules = convert2LevelRuleList(dimRule, gridLevelMap);
        dm.setLevelList(dimLevelRules);

        ErrorInfo errorInfo = null;
        // 非绩效维度需要校验结果类型和数据来源
        if (dmType != 5) {
            // 校验结果类型
            errorInfo = XpdDimRuleValidator.validateResultType(confResultType, dimRule.getResultType());
            if (Objects.nonNull(errorInfo)) {
                dm.setError(errorInfo);
                return dm;
            }
            // 校验数据来源和高级公式
            errorInfo = xpdDimRuleValidator.validateRefIdsAndFormula(xpd.getAomPrjId(), dimRule,
                    dimRuleCalcMap.get(dimRule.getId()));
            if (Objects.nonNull(errorInfo)) {
                dm.setError(errorInfo);
                return dm;
            }

            boolean hasMaxValue = false;
            BigDecimal maxValue = null;
            if (DimResultTypeEnum.byScore(dimRule.getResultType())
                    && (DimLevelTypeEnum.byFixedValue(dimRule.getLevelType()))) {
                hasMaxValue = true;
                maxValue = totalScoreMap.get(String.format(SD_DIM_ID_ORIGIN, dimRule.getSdDimId()));
            }
            // if (DimCalcTypeEnum.bySubDimension(dimRule.getCalcType())
            // && DimResultTypeEnum.byScore(dimRule.getResultType())
            // && (DimLevelTypeEnum.byFixedValue(dimRule.getLevelType()))) {
            // hasMaxValue = true;
            // maxValue = totalScoreMap.get(dimRule.getSdDimId());
            // }
            // 校验分层规则的值
            dm.setError(XpdDimRuleValidator.validateLevelValue(dimRule.getLevelType(), dimLevelRules, hasMaxValue,
                    maxValue));
        } else {
            // 绩效维度，需要校验数据来源
            if (StringUtils.isEmpty(dimRule.getAomActId())) {
                errorInfo = new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST,
                        "数据来源的绩效活动不存在");
            } else {
                // 绩效活动
                AomActvExtBO perfActv = getPerfActvExt(xpd.getOrgId(), xpd, dimRule.getAomActId());
                if (perfActv == null) {
                    errorInfo = new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(),
                            ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST, "数据来源的绩效活动不存在");
                } else {
                    errorInfo = xpdDimRuleValidator.validateLevelValue4Perf(perfActv, dimRule.getCalcType(),
                            dimRule.getLevelType(), dimLevelRules);
                }
            }
            dm.setError(errorInfo);
        }
        return dm;
    }

    /**
     * 填充二级及以下节点，仅在盘点维度选择的计算方式为按子维度结果计算时展示
     *
     * @param curDimRuleDetail   当前需要被填充的维度
     * @param confResultType     全局规则配置中的结果类型
     * @param dimInfoMap         维度基本信息[from facade] key:维度ID-标准
     * @param dimRuleChildrenMap 维度计算规则子节点map key:parentId
     * @param dimRuleCalcMap     维度计算规则map key:维度规则ID value:指标计算列表
     * @param indicatorMap       指标基本信息[from facade] key:指标ID
     */
    private void fillDeeperDimRule(XpdDimRuleDetailInfoDto curDimRuleDetail,
            XpdPO xpd,
            Integer confResultType,
            Map<String, ModelBase4Facade> dimInfoMap,
            Map<String, List<XpdDimRulePO>> dimRuleChildrenMap,
            Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcMap,
            Map<String, IndicatorDto> indicatorMap,
            Map<String, XpdImportPO> importDimMap,
            Map<String, BigDecimal> totalScoreMap) {

        if (Objects.nonNull(curDimRuleDetail.getError())
                && RuleErrorCodeEnum.EMPTY.getCode().equals(curDimRuleDetail.getError().getCode())) {
            return;
        }

        if (!DimCalcTypeEnum.bySubDimension(curDimRuleDetail.getCalcType())) {
            return;
        }

        List<XpdDimRuleDetailInfoDto> subDimDetails = Lists.newArrayList();
        List<XpdDimRulePO> subDimRules = dimRuleChildrenMap.get(curDimRuleDetail.getDimRuleId());
        if (CollectionUtils.isEmpty(subDimRules)) {
            log.warn("LOG20363:");
            return;
        }
        for (XpdDimRulePO subDimRule : subDimRules) {
            XpdDimRuleDetailInfoDto subDm = new XpdDimRuleDetailInfoDto();
            ModelBase4Facade subModelBase = dimInfoMap.get(subDimRule.getSdDimId());
            subDm.setXpdId(curDimRuleDetail.getXpdId());
            subDm.setDmId(subDimRule.getSdDimId());
            subDm.setDimRuleId(subDimRule.getId());
            subDm.setRequireBaseId(subModelBase.getId());
            subDm.setDmName(subModelBase.getDmName());
            subDm.setCalcType(subDimRule.getCalcType());
            subDm.setLevelType(subDimRule.getLevelType());
            subDm.setResultType(subDimRule.getResultType());
            subDm.setDimRuleId(subDimRule.getId());
            subDm.setRuleDesc(subDimRule.getRuleDesc());
            subDm.setEnabled(1);
            subDm.setIsDirectDim(0);
            Integer dmType = DimTypeEnum.isPerfName(subModelBase.getCustomName()) ? DimTypeEnum.PERF.getCode()
                    : subModelBase.getDmType();
            subDm.setDimType(dmType);

            if (importDimMap.containsKey(subModelBase.getDmId())) {
                subDm.setEnabled(0);
            } else {
                // 校验结果类型
                ErrorInfo errorInfo = XpdDimRuleValidator.validateResultType(confResultType,
                        subDimRule.getResultType());
                if (Objects.nonNull(errorInfo)) {
                    subDm.setError(errorInfo);
                }
                if (dimRuleCalcMap.containsKey(subDimRule.getId())) {
                    // 计算规则
                    List<XpdDimRuleCalcPO> subIndicators = dimRuleCalcMap.get(subDimRule.getId());
                    fillAndCheckDimRuleCalcListByIndicator(xpd, subDimRule, subIndicators, indicatorMap, subDm,
                            totalScoreMap);
                } else if (dimRuleChildrenMap.containsKey(subDimRule.getId())) {
                    List<XpdDimRulePO> subsubDimRule = dimRuleChildrenMap.get(subDimRule.getId());
                    fillDimRuleCalcListBySubDim(subsubDimRule, dimInfoMap, totalScoreMap, subDm);
                } else {
                    // 规则没配
                    subDm.setError(new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(),
                            ExceptionKeys.XPD_DIM_RULE_CALC_EMPTY, RULE_CALC_EMPTY));
                }
            }
            fillDeeperDimRule(subDm, xpd, confResultType, dimInfoMap, dimRuleChildrenMap, dimRuleCalcMap, indicatorMap,
                    importDimMap, totalScoreMap);
            subDimDetails.add(subDm);
        }
        curDimRuleDetail.setChildren(subDimDetails);
    }

    /**
     * 末级维度，填充指标计算规则
     *
     * @param indicatorMap  指标基础信息 key:指标ID
     * @param dimRuleDetail 出参
     */
    private void fillAndCheckDimRuleCalcListByIndicator(XpdPO xpd,
            XpdDimRulePO dimRule,
            List<XpdDimRuleCalcPO> dimRuleCalcs,
            Map<String, IndicatorDto> indicatorMap,
            XpdDimRuleDetailInfoDto dimRuleDetail,
            Map<String, BigDecimal> totalScoreMap) {
        String dimRuleId = dimRuleDetail.getDimRuleId();
        // 是否配置计算规则
        List<XpdDimRuleCalcDto> calcList = Lists.newArrayList();
        for (XpdDimRuleCalcPO dimRuleCalc : dimRuleCalcs) {
            XpdDimRuleCalcDto calcDto = new XpdDimRuleCalcDto();
            calcDto.setDimRuleId(dimRuleId);
            calcDto.setSdIndicatorId(dimRuleCalc.getSdIndicatorId());
            IndicatorDto indicator = indicatorMap.get(dimRuleCalc.getSdIndicatorId());
            if (indicator != null) {
                calcDto.setName(indicator.getItemType() == 0 ? indicator.getItemValue() : indicator.getIndicatorName());
            }
            calcDto.setWeight(dimRuleCalc.getWeight());
            calcDto.setTotalScore(totalScoreMap.get(dimRuleCalc.getSdIndicatorId()));
            calcList.add(calcDto);
        }
        dimRuleDetail.setCalcList(calcList);
        dimRuleDetail.setTotalScore(totalScoreMap.get(dimRuleDetail.getDmId()));
        // 校验计算规则和高级公式[末级维度不会有高级公式]
        if (Objects.isNull(dimRuleDetail.getError())) {
            dimRuleDetail
                    .setError(xpdDimRuleValidator.validateRefIdsAndFormula(xpd.getAomPrjId(), dimRule, dimRuleCalcs));
        }
    }

    /**
     * 父级维度，填充维度计算规则
     *
     * @param subDimRuleList 子维度计算规则
     * @param dimInfoMap     维度基础信息 key:维度ID-标准
     * @param dimRuleDetail  出参
     */
    private void fillDimRuleCalcListBySubDim(List<XpdDimRulePO> subDimRuleList,
            Map<String, ModelBase4Facade> dimInfoMap,
            Map<String, BigDecimal> totalScoreMap,
            XpdDimRuleDetailInfoDto dimRuleDetail) {
        // 维度计算规则
        List<XpdDimRuleCalcDto> subDimRuleCalcs = Lists.newArrayList();
        for (XpdDimRulePO subDimRule : subDimRuleList) {
            XpdDimRuleCalcDto subDimCalcDto = new XpdDimRuleCalcDto();
            subDimCalcDto.setDimRuleId(subDimRule.getId());
            subDimCalcDto.setSdDimId(subDimRule.getSdDimId());
            subDimCalcDto.setName(dimInfoMap.get(subDimRule.getSdDimId()).getDmName());
            subDimCalcDto.setWeight(subDimRule.getWeight());
            subDimCalcDto.setTotalScore(totalScoreMap.get(subDimRule.getSdDimId()));
            subDimRuleCalcs.add(subDimCalcDto);
        }
        dimRuleDetail.setCalcList(subDimRuleCalcs);

    }

    /**
     * 创建全局规则配置
     *
     * @param confDto 入参
     */
    public String createXpdRuleConfGlobal(String orgId, String userId, XpdRuleConfDto confDto) {

        String ruleConfId = null;
        String xpdId = confDto.getXpdId();
        XpdPO xpd = checkXpdExist(xpdId);
        String lockKey = String.format(RedisKeys.LK_XPD_RULE_GEN, orgId, xpdId);
        if (lockService.tryLock(lockKey, 30, TimeUnit.SECONDS)) {
            try {
                checkConfDto(confDto);
                // 没有配置ID，新增
                if (StringUtils.isEmpty(confDto.getRuleConfId())) {
                    // 做下校验，防止同一时间有人在编辑
                    XpdRuleConfPO ruleConf = xpdRuleConfMapper.selectByXpdId(orgId, confDto.getXpdId());
                    // 盘点项目配置已存在
                    Validate.isTrue(Objects.isNull(ruleConf), ExceptionKeys.XPD_RULE_CONF_EXIST);

                    ruleConfId = initRuleConfGlobal(orgId, userId, xpd, confDto);
                }
            }
            // catch (Exception e) {
            // log.error("LOG62680:初始化全局配置规则出错：orgId={}, xpdId={}", orgId, xpdId, e);
            // // 他人正在生成维度规则，请稍等
            // throw e;
            // }
            finally {
                lockService.unLock(lockKey);
                log.debug("LOG62680,解锁：key={}", lockKey);
            }
        }
        return ruleConfId;
    }

    /**
     * 解锁
     */
    public void unlock(String orgId, String xpdId) {
        String lockKey = String.format(RedisKeys.LK_XPD_RULE_GEN, orgId, xpdId);
        lockService.unLock(lockKey);
    }

    /**
     * 创建/更新全局规则配置
     *
     * @param confDto 入参
     */
    public void upXpdRuleConfGlobal(String orgId, String userId, XpdRuleConfDto confDto) {

        String xpdId = confDto.getXpdId();
        String lockKey = String.format(RedisKeys.LK_XPD_RULE_GEN, orgId, xpdId);
        if (lockService.tryLock(lockKey, 30, TimeUnit.SECONDS)) {
            try {
                checkConfDto(confDto);
                XpdPO xpd = checkXpdExist(xpdId);
                // 更新规则数据
                XpdRuleConfPO ruleConf = checkXpdRuleConfExist(confDto.getRuleConfId());
                // 乐观锁：版本号不一致说明被修改过
                if (!ruleConf.getVersion().equals(confDto.getRuleConfVersion())) {
                    // 项目配置规则已被修改，请刷新页面重新再试
                    throw new ApiException(ExceptionKeys.XPD_RULE_CONF_MODIFIED);
                }

                upRuleConfGlobal(orgId, userId, xpd, ruleConf, confDto);
            } catch (Exception e) {
                log.error("LOG42326:快速生成规则出错：orgId={}, xpdId={}", orgId, xpdId, e);
                // 他人正在生成维度规则，请稍等
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        }
    }

    /**
     * 执行快速生成规则
     * 删除旧维度规则，生成新维度规则
     *
     * @param orgId       机构ID
     * @param userId      用户ID
     * @param confFastDto 规则配置入参
     */
    public void upRuleConfFast(String orgId, String userId, XpdRuleConfFastDto confFastDto) {
        String xpdId = confFastDto.getXpdId();
        String lockKey = String.format(RedisKeys.LK_XPD_RULE_GEN, orgId, xpdId);
        if (lockService.tryLock(lockKey, 30, TimeUnit.SECONDS)) {
            try {
                // 更新
                XpdRuleConfPO xpdRuleConf = checkXpdRuleConfExistByXpdId(orgId, confFastDto.getXpdId());
                XpdPO xpd = checkXpdExist(xpdId);
                confFastDto.setScoreSystem(xpdRuleConf.getScoreSystem());
                execRuleConfFast(orgId, userId, xpd, xpdRuleConf, confFastDto);
            } catch (Exception e) {
                log.error("LOG42336:快速生成规则出错：orgId={}, xpdId={}", orgId, xpdId, e);
                // 他人正在生成维度规则，请稍等
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        }
    }

    /**
     * 执行快速生成规则
     * 1.清空所有旧维度规则
     * 2.生成所有新维度规则
     *
     * @param orgId    机构ID
     * @param userId   用户ID
     * @param xpd      项目信息
     * @param ruleConf 项目配置规则
     * @param confDto  入参
     */
    private void execRuleConfFast(String orgId, String userId, XpdPO xpd, XpdRuleConfPO ruleConf,
            XpdRuleConfFastDto confDto) {
        String xpdId = xpd.getId();

        // 校验分层规则
        List<XpdDimLevelRuleDto> levelRuleList = confDto.getLevelRuleList();
        ErrorInfo errorInfo = XpdDimRuleValidator.validateLevelValue(confDto.getLevelType(), levelRuleList, false,
                null);
        if (Objects.nonNull(errorInfo)) {
            throw new ApiException(errorInfo.getErrKey());
        }

        // 重新生成规则
        XpdRuleCombDto ruleCombDto = ruleConfComponent.genRuleCombDataWhenExecFastRule(orgId, userId, xpd, ruleConf,
                confDto);

        getSelfProxy().execRuleConfFastTrans(orgId, userId, xpdId, ruleCombDto);
    }

    /**
     * 执行快速生成规则
     * 1.清空所有旧维度规则
     * 2.生成所有新维度规则
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void execRuleConfFastTrans(String orgId, String userId, String xpdId, XpdRuleCombDto ruleCombDto) {

        xpdDimRuleMapper.deleteByXpdId(orgId, userId, xpdId);
        xpdDimRuleCalcMapper.deleteByXpdId(orgId, userId, xpdId);

        if (Objects.nonNull(ruleCombDto)) {
            if (Objects.nonNull(ruleCombDto.getNewOrUpXpdRuleConf())) {
                xpdRuleConfMapper.insertOrUpdate(ruleCombDto.getNewOrUpXpdRuleConf());
            }
            if (Objects.nonNull(ruleCombDto.getDimRuleCombDto())) {
                if (CollectionUtils.isNotEmpty(ruleCombDto.getDimRuleCombDto().getNewDimRuleList())) {
                    xpdDimRuleMapper.batchInsert(ruleCombDto.getDimRuleCombDto().getNewDimRuleList());
                }
                if (CollectionUtils.isNotEmpty(ruleCombDto.getDimRuleCombDto().getNewDimRuleCalcList())) {
                    xpdDimRuleCalcMapper.batchInsert(ruleCombDto.getDimRuleCombDto().getNewDimRuleCalcList());
                }
            }
        }
    }

    private void checkConfDto(XpdRuleConfDto confDto) {
        if (StringUtils.isNotEmpty(confDto.getRuleConfId())) {
            // 更新的话版本号不能为空[盘点项目规则版本号不能为空]
            Validate.isTrue(confDto.getRuleConfVersion() != null, ExceptionKeys.XPD_RULE_CONF_VERSION_EMPTY);
        }
    }

    /**
     * 初始化盘点项目规则
     * 1.宫格模板相关数据复制
     * 2.项目/维度配置相关数据复制
     * -2.1.生成项目规则配置
     *
     * @param orgId   机构ID
     * @param userId  用户ID
     * @param confDto 入参
     */
    public String initRuleConfGlobal(String orgId, String userId, XpdPO xpd, XpdRuleConfDto confDto) {
        // 1.宫格模板数据复制
        List<String> sdDimIds = StreamUtil.mapList(confDto.getDimList(), DimSimpleInfoDto::getDmId);
        XpdGridCombDto gridCombDto = xpdGridReplicator.copyWhenXpdConfInit(orgId, userId, confDto.getXpdId(),
                confDto.getGridId(), sdDimIds);

        // 2.项目规则配置
        XpdRuleCombDto ruleCombDto = ruleConfComponent.genRuleCombDataWhenInitGlobal(orgId, userId,
                gridCombDto.getNewXpdGrid(),
                xpd, null, gridCombDto.getNewGridLevelList(),
                gridCombDto.getIdMap(),
                confDto);
        // 保存操作 transaction
        getSelfProxy().initRuleConfGlobalTrans(gridCombDto, ruleCombDto);
        reCalcRuleDisplay(xpd);
        generateDimCombs(orgId, userId, confDto.getXpdId(), gridCombDto.getNewXpdGrid().getId());
        return ruleCombDto.getNewOrUpXpdRuleConf().getId();
    }

    private void reCalcRuleDisplay(XpdPO xpd) {
        List<XpdLevelPO> levelList = xpdLevelMapper.listByXpdId(xpd.getOrgId(), xpd.getId());
        if (CollectionUtils.isEmpty(levelList)) {
            return;
        }
        List<Pair<XpdLevelPO, SpRuleBean>> list = BeanCopierUtil.convertList(levelList,
                level -> Pair.of(level, JSON.parseObject(level.getFormula(), SpRuleBean.class)));
        RuleMainBase mainData = new RuleMainBase();
        mainData.setOrgId(xpd.getOrgId());
        mainData.setBizId(xpd.getId());
        spRuleService.calcRuleDisplay(mainData, list, Pair::getValue, (pair, display) -> {
            pair.getKey().setFormulaDisplay(display);
        });
        xpdLevelMapper.batchUpdateFormulaDisplay(levelList);
    }

    /**
     * 更新配置规则
     * 1.宫格模板变更
     * 2.结果类型变更
     * 3.维度删除
     * 4.维度新增
     *
     * @param orgId    机构ID
     * @param userId   用户ID
     * @param xpd      项目信息
     * @param ruleConf 项目配置规则
     * @param confDto  入参
     */
    private void upRuleConfGlobal(String orgId, String userId, XpdPO xpd, XpdRuleConfPO ruleConf,
            XpdRuleConfDto confDto) {
        String xpdId = xpd.getId();
        // 0：无变化 1：新增维度 2：删除维度，维度没有被项目计算规则使用 3：删除的维度被项目结算规则使用 4：修改结果类型 5：修改宫格模板 6：修改分制
        XpdRuleModifyDto modifyTypeDto = checkRuleConfModifyType(orgId, confDto);
        Integer modifyType = modifyTypeDto.getModifyType();
        if (XpdRuleConfModifyTypeEnum.notModified(modifyType)) {
            return;
        }

        // 修改配置
        XpdGridCombDto gridCombDto = XpdGridCombDto.builder().build();
        XpdRuleCombDto ruleCombDto = XpdRuleCombDto.builder()
                .newOrUpXpdRuleConf(ruleConf)
                .build();

        List<String> delSdDimIds = modifyTypeDto.getDelSdDimIds();
        List<String> newSdDimIds = modifyTypeDto.getNewSdDimIds();
        if (CollectionUtils.isNotEmpty(delSdDimIds)) {
            ruleConfComponent.genRuleCombDataWhenDelDim(orgId, xpdId, delSdDimIds, ruleCombDto);
        }
        if (CollectionUtils.isNotEmpty(newSdDimIds)) {
            ruleConfComponent.genDimRuleWhenAddDimNoSub(orgId, userId, xpd, newSdDimIds, ruleCombDto);
        }

        ruleConf.setTemplateGridId(confDto.getGridId());
        ruleConf.setResultType(confDto.getResultType());
        ruleConf.setScoreSystem(confDto.getScoreSystem());
        ruleConf.setUpdateUserId(userId);
        ruleConf.setUpdateTime(LocalDateTime.now());
        ruleCombDto.setNewOrUpXpdRuleConf(ruleConf);
        if (XpdRuleConfModifyTypeEnum.confModify(modifyType)) {
            // 修改宫格模板
            if (XpdRuleConfModifyTypeEnum.gridModify(modifyType)) {
                List<String> sdDimIds = StreamUtil.mapList(confDto.getDimList(), DimSimpleInfoDto::getDmId);
                gridCombDto = xpdGridReplicator.copyWhenXpdConfGridModify(orgId, userId, xpdId,
                        ruleConf.getGridId(),
                        confDto.getGridId(), sdDimIds);
                ruleConfComponent.initXpdConf(orgId, userId, gridCombDto.getNewXpdGrid(), gridCombDto.getIdMap(),
                        confDto, ruleCombDto);
                List<XpdDimLevelRuleDto> dimLevelRuleDtos = ruleConfComponent
                        .initDimLevelRules(gridCombDto.getNewXpdGrid(), gridCombDto.getNewGridLevelList());
                XpdRuleConfFastDto confFastDto = JSON.parseObject(ruleConf.getSnap(), XpdRuleConfFastDto.class);
                confFastDto.setLevelType(confFastDto.getLevelType() != null ? confFastDto.getLevelType()
                        : XpdLevelTypeEnum.getDefault().getCode());
                confFastDto.setLevelPriority(confFastDto.getLevelPriority() != null ? confFastDto.getLevelPriority()
                        : XpdLevelPriorityEnum.getDefault().getCode());
                confFastDto.setLevelRuleList(dimLevelRuleDtos);
                ruleConf.setSnap(JSON.toJSONString(confFastDto));
            }

            String newXpdGridId = ruleConf.getGridId();
            if (Objects.nonNull(gridCombDto.getNewXpdGrid())) {
                log.debug("LOG20633:{}", newXpdGridId);
                newXpdGridId = gridCombDto.getNewXpdGrid().getId();
            }
            ruleConf.setGridId(newXpdGridId);

            // 是否需要清空计算结果
            if (XpdRuleConfModifyTypeEnum.needClearResult(modifyType)) {
                log.debug("LOG20623:");
                clearUserResult(orgId, userId, xpdId, XpdRuleConfModifyTypeEnum.needClearImptResult(modifyType));
            }

            getSelfProxy().updRuleConfGlobalTransWhenConfModify(
                orgId, userId, xpdId, gridCombDto, ruleCombDto, XpdRuleConfModifyTypeEnum.gridModify(modifyType));

            // 重新渲染规则展示用文本
            reCalcRuleDisplay(xpd);
        } else {
            // 修改维度
            getSelfProxy().updRuleConfGlobalTransWhenDimModify(orgId, userId, xpdId, ruleCombDto);
        }

        // 生成维度组合
        generateDimCombs(orgId, userId, xpdId, ruleConf.getGridId());
    }

    private void clearUserResult(String orgId, String userId, String xpdId, boolean needClearImptResult) {
        xpdResultCalcService.clearCalcResult(orgId, xpdId);
        if (needClearImptResult) {
            xpdActvImptService.deleteImportAct(orgId, xpdId, userId);
        }
    }

    /**
     * 校验编辑配置规则的变更类型
     *
     * @param orgId   机构ID
     * @param confDto 入参
     * @return 0：无变化 1：新增维度 2：删除维度 3：修改配置 4：修改宫格模板
     */
    public XpdRuleModifyDto checkRuleConfModifyType(String orgId, XpdRuleConfDto confDto) {

        XpdRuleModifyDto modifyDto = new XpdRuleModifyDto();
        modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.NONE.getCode());

        // 盘点项目配置规则ID不能为空
        Validate.isTrue(StringUtils.isNotEmpty(confDto.getRuleConfId()), ExceptionKeys.XPD_RULE_CONF_ID_EMPTY);
        Validate.isTrue(StringUtils.isNotEmpty(confDto.getXpdId()), ExceptionKeys.XPD_ID_EMPTY);
        // 盘点维度不能为空
        Validate.isTrue(CollectionUtils.isNotEmpty(confDto.getDimList()), ExceptionKeys.XPD_RULE_CONF_DIMIDS_EMPTY);
        // 盘点维度最少2个
        Validate.isTrue(confDto.getDimList().size() >= 2, ExceptionKeys.XPD_RULE_CONF_DIMIDS_MIN);
        // 盘点类型不能为空
        Validate.isTrue(Objects.nonNull(confDto.getResultType()), ExceptionKeys.XPD_RULE_CONF_RESULTTYPE_EMPTY);
        // 宫格模板不能为空
        Validate.isTrue(StringUtils.isNotEmpty(confDto.getGridId()), ExceptionKeys.XPD_RULE_CONF_GRIDID_EMPTY);

        XpdRuleConfPO ruleConf = checkXpdRuleConfExist(confDto.getRuleConfId());

        boolean resultTypeModify = !ruleConf.getResultType().equals(confDto.getResultType());
        boolean gridIdModify = !ruleConf.getTemplateGridId().equals(confDto.getGridId());
        boolean scoreSystemModify = !Objects.equals(ruleConf.getScoreSystem(), confDto.getScoreSystem());

        List<String> sdDimIds = xpdDimMapper.listByXpdId(orgId, confDto.getXpdId()).stream().map(XpdDimPO::getSdDimId)
                .collect(Collectors.toList());
        List<String> sdDimIdsIn = StreamUtil.mapList(confDto.getDimList(), DimSimpleInfoDto::getDmId);
        List<String> delSdDimIds = ListUtil.difference(sdDimIds, sdDimIdsIn);
        List<String> newSdDimIds = ListUtil.difference(sdDimIdsIn, sdDimIds);
        modifyDto.setDelSdDimIds(delSdDimIds);
        modifyDto.setNewSdDimIds(newSdDimIds);

        // 修改配置
        if (gridIdModify) {
            modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.GRID_MODIFY.getCode());
            return modifyDto;
        }
        if (resultTypeModify) {
            modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.RESULT_TYPE_MODIFY.getCode());
            return modifyDto;
        }
        if (scoreSystemModify) {
            modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.SCORE_SYSTEM_MODIFY.getCode());
            return modifyDto;
        }

        List<String> usedSdDimIds = new ArrayList<>();
        XpdRulePO xpdRule = xpdRuleMapper.getByXpdId(orgId, confDto.getXpdId());
        if (xpdRule != null) {
            // 结果类型：维度结果分层
            if (XpdResultTypeEnum.byDimLevelResult(xpdRule.getResultType())) {
                List<XpdLevelPO> levelList = xpdLevelMapper.listByXpdRuleId(orgId, xpdRule.getId());
                usedSdDimIds = extractSdDimIdsFrom(levelList);
            } else if (XpdResultTypeEnum.byScore(xpdRule.getResultType())) {
                // 项目中引用的维度
                usedSdDimIds = xpdRuleCalcDimMapper.getByXpdId(orgId, confDto.getXpdId()).stream()
                        .map(XpdRuleCalcDimPO::getSdDimId).collect(Collectors.toList());
            }
        }
        List<String> intersection = ListUtil.intersection(delSdDimIds, usedSdDimIds);
        // 删除被项目结果规则引用的维度
        if (CollectionUtils.isNotEmpty(intersection)) {
            modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.DEL_USED_DIM.getCode());
        } else if (CollectionUtils.isNotEmpty(delSdDimIds)) {
            // 删除维度，维度没有被项目计算规则引用
            modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.DEL_UNUSED_DIM.getCode());
        } else if (CollectionUtils.isNotEmpty(newSdDimIds)) {
            // 新增维度
            modifyDto.setModifyType(XpdRuleConfModifyTypeEnum.ADD_DIM.getCode());
        }
        return modifyDto;
    }

    private List<String> extractSdDimIdsFrom(List<XpdLevelPO> levelList) {
        Set<String> sdDimIds = new HashSet<>();
        if (CollectionUtils.isEmpty(levelList)) {
            return new ArrayList<>();
        }
        for (XpdLevelPO level : levelList) {
            SpRuleBean spRuleBean = BeanHelper.json2Bean(level.getFormula(), SpRuleBean.class);
            if (Objects.isNull(spRuleBean) || CollectionUtils.isEmpty(spRuleBean.getConditions())) {
                continue;
            }

            for (SpRuleGroupBean condition : spRuleBean.getConditions()) {
                if (CollectionUtils.isEmpty(condition.getRules())) {
                    continue;
                }
                for (SpRuleColumnBean rule : condition.getRules()) {
                    sdDimIds.add(rule.getId());
                }
            }
        }
        return new ArrayList<>(sdDimIds);
    }

    /**
     * 初始化项目规则全局配置
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void initRuleConfGlobalTrans(XpdGridCombDto gridCombDto, XpdRuleCombDto ruleCombDto) {

        if (Objects.nonNull(gridCombDto)) {
            if (Objects.nonNull(gridCombDto.getNewXpdGrid())) {
                xpdGridMapper.insert(gridCombDto.getNewXpdGrid());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridCellList())) {
                xpdGridCellMapper.batchInsert(gridCombDto.getNewGridCellList());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewDimCombList())) {
                xpdDimCombMapper.batchInsert(gridCombDto.getNewDimCombList());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridDimCombList())) {
                xpdGridDimCombMapper.batchInsert(gridCombDto.getNewGridDimCombList());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridLevelList())) {
                xpdGridLevelMapper.batchInsert(gridCombDto.getNewGridLevelList());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridRatioList())) {
                xpdGridRatioMapper.batchInsert(gridCombDto.getNewGridRatioList());
            }
        }

        if (Objects.nonNull(ruleCombDto)) {
            if (Objects.nonNull(ruleCombDto.getNewOrUpXpdRuleConf())) {
                xpdRuleConfMapper.insertOrUpdate(ruleCombDto.getNewOrUpXpdRuleConf());
            }
            if (Objects.nonNull(ruleCombDto.getNewXpdRule())) {
                xpdRuleMapper.insert(ruleCombDto.getNewXpdRule());
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getNewLevelList())) {
                xpdLevelMapper.batchInsert(ruleCombDto.getNewLevelList());
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getNewDimList())) {
                xpdDimMapper.batchInsert(ruleCombDto.getNewDimList());
            }
        }
    }

    /**
     * 修改配置
     * 更新配置
     * 清空项目规则（项目规则，项目计算规则）
     * 清空维度规则（维度规则，维度计算规则）
     * 清空原宫格模板配置，新增新宫格模板配置
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void updRuleConfGlobalTransWhenConfModify(String orgId, String userId, String xpdId,
            XpdGridCombDto gridCombDto,
            XpdRuleCombDto ruleCombDto,
            boolean clearXpdRule) {

        if (clearXpdRule) {
            log.debug("LOG20613:");
            xpdRuleMapper.deleteByXpdId(orgId, userId, xpdId);
            xpdLevelMapper.deleteByXpdId(orgId, userId, xpdId);
            xpdRuleCalcDimMapper.deleteByXpdId(orgId, xpdId, userId);
            xpdRuleCalcIndicatorMapper.deleteByXpdId(orgId, xpdId, userId);
        }

        xpdDimRuleMapper.deleteByXpdId(orgId, userId, xpdId);
        xpdDimRuleCalcMapper.deleteByXpdId(orgId, userId, xpdId);
        if (Objects.nonNull(ruleCombDto)) {
            if (Objects.nonNull(ruleCombDto.getNewOrUpXpdRuleConf())) {
                xpdRuleConfMapper.insertOrUpdate(ruleCombDto.getNewOrUpXpdRuleConf());
            }
            if (Objects.nonNull(ruleCombDto.getNewXpdRule())) {
                xpdRuleMapper.insert(ruleCombDto.getNewXpdRule());
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getNewLevelList())) {
                xpdLevelMapper.batchInsert(ruleCombDto.getNewLevelList());
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getDelSdDimIdList())) {
                xpdDimMapper.deleteBySdDimIds(orgId, xpdId, ruleCombDto.getDelSdDimIdList(), userId);
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getNewDimList())) {
                xpdDimMapper.batchInsert(ruleCombDto.getNewDimList());
            }
        }
        if (Objects.nonNull(gridCombDto)) {
            String delGridId = gridCombDto.getDelXpdGridId();
            if (StringUtils.isNotEmpty(gridCombDto.getDelXpdGridId())) {
                xpdGridMapper.deleteById(delGridId, userId);
                xpdGridCellMapper.deleteGrid(orgId, delGridId, userId);
                xpdGridDimCombMapper.deleteByGridId(orgId, delGridId, userId);
                xpdGridLevelMapper.deleteGridLevel(orgId, delGridId, userId);
                xpdGridRatioMapper.deleteGridRatio(orgId, delGridId, userId);
            }

            if (Objects.nonNull(gridCombDto.getNewXpdGrid())) {
                xpdGridMapper.insert(gridCombDto.getNewXpdGrid());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridCellList())) {
                xpdGridCellMapper.batchInsert(gridCombDto.getNewGridCellList());
            }
            // if (CollectionUtils.isNotEmpty(gridCombDto.getNewDimCombList())) {
            // xpdDimCombMapper.batchInsert(gridCombDto.getNewDimCombList());
            // }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridDimCombList())) {
                xpdGridDimCombMapper.batchInsert(gridCombDto.getNewGridDimCombList());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridLevelList())) {
                xpdGridLevelMapper.batchInsert(gridCombDto.getNewGridLevelList());
            }
            if (CollectionUtils.isNotEmpty(gridCombDto.getNewGridRatioList())) {
                xpdGridRatioMapper.batchInsert(gridCombDto.getNewGridRatioList());
            }
        }
    }

    /**
     * 修改维度
     * 更新配置
     * 新增维度
     * 删除维度 同时 删除维度规则
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void updRuleConfGlobalTransWhenDimModify(String orgId, String userId, String xpdId, XpdRuleCombDto ruleCombDto) {
        if (Objects.nonNull(ruleCombDto)) {
            if (CollectionUtils.isNotEmpty(ruleCombDto.getDelSdDimIdList())) {
                xpdDimMapper.deleteBySdDimIds(orgId, xpdId, ruleCombDto.getDelSdDimIdList(), userId);
                // xpdImportActService.deletedImportActByDimIds(orgId, xpdId,
                // ruleCombDto.getDelSdDimIdList(), userId);
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getDelDimRuleIdList())) {
                xpdDimRuleMapper.deleteByIds(userId, ruleCombDto.getDelDimRuleIdList());
            }
            if (CollectionUtils.isNotEmpty(ruleCombDto.getDelDimRuleCalcIdList())) {
                xpdDimRuleCalcMapper.deleteByIds(userId, ruleCombDto.getDelDimRuleCalcIdList());
            }

            if (CollectionUtils.isNotEmpty(ruleCombDto.getNewDimList())) {
                xpdDimMapper.batchInsert(ruleCombDto.getNewDimList());
            }
            if (Objects.nonNull(ruleCombDto.getNewOrUpXpdRuleConf())) {
                xpdRuleConfMapper.insertOrUpdate(ruleCombDto.getNewOrUpXpdRuleConf());
            }
        }
    }

    private List<DimSimpleInfoDto> convert2DimInfoList(List<XpdDimPO> dimList, List<ModelBase4Facade> sdDimList) {

        if (CollectionUtils.isEmpty(dimList) || CollectionUtils.isEmpty(sdDimList)) {
            return Lists.newArrayList();
        }

        Map<String, ModelBase4Facade> sdDimIdMap = StreamUtil.list2map(sdDimList, ModelBase4Facade::getDmId);
        List<DimSimpleInfoDto> dimInfoList = Lists.newArrayList();
        for (XpdDimPO xpdDim : dimList) {
            if (sdDimIdMap.containsKey(xpdDim.getSdDimId())) {
                ModelBase4Facade sdDim = sdDimIdMap.get(xpdDim.getSdDimId());
                DimSimpleInfoDto dimInfoDto = DimSimpleInfoDto.builder()
                        .dmId(xpdDim.getSdDimId())
                        .dmName(sdDim.getDmName())
                        // .orderIndex(sdDim.getOrderIndex())
                        .build();
                dimInfoList.add(dimInfoDto);
            }
        }
        return dimInfoList;
    }

    /**
     * 校验盘点的详细规则
     *
     * @param orgId            机构ID
     * @param xpdId            盘点项目ID
     * @param emptyConfAsError 对还未配置规则的处理
     *                         true: 没配置规则报错 false: 没配置不报错，直接不校验
     * @return ErrorInfo 没有错误返回null
     */
    public ErrorInfo checkXpdRuleConf4Common(String orgId, String xpdId, boolean emptyConfAsError) {

        XpdPO xpd = checkXpdExist(xpdId);
        String modelId = xpd.getModelId();
        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        if (Objects.isNull(xpdRuleConf)) {
            if (emptyConfAsError) {
                return new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_RULE_CONF_NOT_EXIST,
                        "项目规则未配置");
            }
            return null;
        }

        XpdRulePO xpdRule = xpdRuleMapper.getByXpdId(orgId, xpd.getId());
        // 规则没配
        if (Objects.isNull(xpdRule)) {
            return new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_RULE_CALC_EMPTY, RULE_CALC_EMPTY);
        }

        // 项目的维度
        List<XpdDimPO> xpdDimList = xpdDimMapper.listByXpdId(orgId, xpdId);
        List<String> sdDimIds = StreamUtil.mapList(xpdDimList, XpdDimPO::getSdDimId);
        if (CollectionUtils.isEmpty(sdDimIds)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CONF_DIM_EMPTY, "项目维度未配置");
        }

        // 维度规则
        List<XpdDimRulePO> dimRuleList = xpdDimRuleMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRuleList);
        Map<String, List<XpdDimRulePO>> dimRuleChildrenMap = getDimRuleParentIdMap(dimRuleList);

        // 维度数据
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, modelId, sdDimIds);
        Map<String, ModelBase4Facade> dimInfoMap = StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId);

        // 维度的计算规则-指标
        Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcMap = xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId).stream()
                .collect(Collectors.groupingBy(XpdDimRuleCalcPO::getDimRuleId));
        // 模型下的所有指标
        Map<String, IndicatorDto> indicatorMap = StreamUtil.list2map(spsdAclService.getLastIndicators(orgId, modelId),
                IndicatorDto::getItemId);

        // 维度的分层规则
        Map<String, XpdGridLevelPO> gridLevelMap = StreamUtil.list2map(xpdGridLevelMapper.listByXpdId(orgId, xpdId),
                XpdGridLevelPO::getId);

        ErrorInfo xpdError;
        // 校验项目规则
        // 项目的计算方式：按指标结果计算
        List<XpdIndicatorCalcBean> indicatorCalcList = new ArrayList<>();
        Map<String, BigDecimal> totalScoreMap = calcXpdTotalScoreAndDimScores(orgId, xpd, xpdRule, true, indicatorCalcList);
        if (XpdCalcTypeEnum.byIndicator(xpdRule.getCalcType())) {
            xpdError = xpdRuleValidator.validateByIndicator(orgId, xpd, xpdRule, totalScoreMap);
        } else {
            // 项目的计算方式：按维度结果计算
            xpdError = xpdRuleValidator.validateByDim(orgId, xpdRuleConf.getResultType(), xpdRule, dimRuleList,
                    totalScoreMap);
        }
        if (Objects.nonNull(xpdError)) {
            return xpdError;
        }

        // 校验维度下的结果
        List<XpdDimRuleDetailInfoDto> dimRuleDetails = Lists.newArrayList();
        Map<String, XpdImportPO> importDimMap = StreamUtil.list2map(
                xpdImportMapper.findByXpdIdAndImportType(orgId, xpdId, XpdImportTypeEnum.DIM.getCode(), true),
                XpdImportPO::getSdDimId);

        for (String sdDimId : sdDimIds) {
            XpdDimRuleDetailInfoDto dimRuleInfo = fillAndCheckFirstDimRule(sdDimId, xpd, xpdRuleConf.getResultType(),
                    dimInfoMap, dimRuleMap, gridLevelMap, dimRuleCalcMap,
                    importDimMap, totalScoreMap);
            if (Objects.nonNull(dimRuleInfo.getError())) {
                return dimRuleInfo.getError();
            }
            dimRuleDetails.add(dimRuleInfo);
        }

        // 校验二级及以下节点
        for (XpdDimRuleDetailInfoDto dimRuleDetail : dimRuleDetails) {
            if (StringUtils.isNotEmpty(dimRuleDetail.getDimRuleId())) {
                ErrorInfo errorInfo = checkDeeperDimRule(dimRuleDetail, xpd, xpdRuleConf.getResultType(), dimInfoMap,
                        dimRuleChildrenMap, dimRuleCalcMap,
                        indicatorMap, importDimMap, totalScoreMap);
                if (Objects.nonNull(errorInfo)) {
                    return errorInfo;
                }
            }
        }

        return null;
    }

    /**
     * 校验二级及以下节点，仅在盘点维度选择的计算方式为按子维度结果计算时展示
     *
     * @param curDimRuleDetail   当前需要被填充的维度
     * @param confResultType     全局规则配置中的结果类型
     * @param dimInfoMap         维度基本信息[from facade] key:维度ID-标准
     * @param dimRuleChildrenMap 维度计算规则子节点map key:parentId
     * @param dimRuleCalcMap     维度计算规则map key:维度规则ID value:指标计算列表
     * @param indicatorMap       指标基本信息[from facade] key:指标ID
     */
    private ErrorInfo checkDeeperDimRule(XpdDimRuleDetailInfoDto curDimRuleDetail,
            XpdPO xpd,
            Integer confResultType,
            Map<String, ModelBase4Facade> dimInfoMap,
            Map<String, List<XpdDimRulePO>> dimRuleChildrenMap,
            Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcMap,
            Map<String, IndicatorDto> indicatorMap,
            Map<String, XpdImportPO> importDimMap,
            Map<String, BigDecimal> totalScoreMap) {

        if (Objects.nonNull(curDimRuleDetail.getError())
                && RuleErrorCodeEnum.EMPTY.getCode().equals(curDimRuleDetail.getError().getCode())) {
            return null;
        }

        if (!DimCalcTypeEnum.bySubDimension(curDimRuleDetail.getCalcType())) {
            return null;
        }

        List<XpdDimRulePO> subDimRules = Optional.ofNullable(dimRuleChildrenMap.get(curDimRuleDetail.getDimRuleId()))
            .orElse(Lists.emptyList());
        for (XpdDimRulePO subDimRule : subDimRules) {
            XpdDimRuleDetailInfoDto subDm = new XpdDimRuleDetailInfoDto();
            ModelBase4Facade subModelBase = dimInfoMap.get(subDimRule.getSdDimId());
            subDm.setXpdId(curDimRuleDetail.getXpdId());
            subDm.setDmId(subDimRule.getSdDimId());
            subDm.setDimRuleId(subDimRule.getId());
            subDm.setRequireBaseId(subModelBase.getId());
            subDm.setDmName(subModelBase.getDmName());
            subDm.setCalcType(subDimRule.getCalcType());
            subDm.setLevelType(subDimRule.getLevelType());
            subDm.setResultType(subDimRule.getResultType());
            subDm.setDimRuleId(subDimRule.getId());
            subDm.setRuleDesc(subDimRule.getRuleDesc());
            subDm.setEnabled(1);
            subDm.setIsDirectDim(0);
            Integer dmType = DimTypeEnum.isPerfName(subModelBase.getCustomName()) ? DimTypeEnum.PERF.getCode()
                    : subModelBase.getDmType();
            subDm.setDimType(dmType);

            if (importDimMap.containsKey(subModelBase.getDmId())) {
                subDm.setEnabled(0);
            } else {
                // 校验结果类型
                ErrorInfo errorInfo = XpdDimRuleValidator.validateResultType(confResultType,
                        subDimRule.getResultType());
                if (Objects.nonNull(errorInfo)) {
                    return errorInfo;
                }
                if (dimRuleCalcMap.containsKey(subDimRule.getId())) {
                    // 计算规则
                    List<XpdDimRuleCalcPO> subIndicators = dimRuleCalcMap.get(subDimRule.getId());
                    fillAndCheckDimRuleCalcListByIndicator(xpd, subDimRule, subIndicators, indicatorMap, subDm,
                            totalScoreMap);

                    if (Objects.nonNull(subDm.getError())) {
                        return subDm.getError();
                    }
                } else if (dimRuleChildrenMap.containsKey(subDimRule.getId())) {
                    List<XpdDimRulePO> subsubDimRule = dimRuleChildrenMap.get(subDimRule.getId());
                    fillDimRuleCalcListBySubDim(subsubDimRule, dimInfoMap, totalScoreMap, subDm);

                    if (Objects.nonNull(subDm.getError())) {
                        return subDm.getError();
                    }
                } else {
                    // 规则没配
                    return new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_DIM_RULE_CALC_EMPTY,
                            RULE_CALC_EMPTY);
                }
            }
            ErrorInfo errorInfo = checkDeeperDimRule(subDm, xpd, confResultType, dimInfoMap, dimRuleChildrenMap,
                    dimRuleCalcMap, indicatorMap, importDimMap, totalScoreMap);
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
        }
        return null;
    }

    /**
     * 查询项目绑定的维度列表
     *
     * @param userCache   用户缓存信息
     * @param xpdId       项目ID
     * @param excludePerf
     * @return 维度列表
     */
    public List<XpdDimVo> listXpdDim(UserCacheDetail userCache, String xpdId, Integer excludePerf) {
        String orgId = userCache.getOrgId();
        List<XpdDimPO> xpdDimList = xpdDimMapper.selectByXpdId(orgId, xpdId, excludePerf);
        // 国际化翻译
        List<XpdDimVo> xpdDimVos = translateDimList(userCache, orgId, xpdDimList);
        // 获取维度规则说明
        List<String> sdDimIds = StreamUtil.mapList(xpdDimList, XpdDimPO::getSdDimId);
        List<XpdDimRulePO> xpdDimRules = xpdDimRuleMapper.listBySdDimIds(orgId, xpdId, sdDimIds);
        Map<String, XpdDimRulePO> ruleMap = StreamUtil.list2map(xpdDimRules, XpdDimRulePO::getSdDimId);
        xpdDimVos.forEach(vo -> {
            XpdDimRulePO sdDimRule = ruleMap.get(vo.getSdDimId());
            vo.setSdDimRuleDesc(sdDimRule != null ? sdDimRule.getRuleDesc() : "");
        });
        return xpdDimVos;
    }

    @Nonnull
    private List<XpdDimVo> translateDimList(UserCacheDetail userCache, String orgId, List<XpdDimPO> xpdDimList) {
        Map<String, DimensionList4Get> baseDimMap = getSdDimBaseInfo(orgId, xpdDimList);

        List<XpdDimVo> xpdDimVos = xpdDimList.stream()
                .map(xpdDim -> {
                    XpdDimVo xpdDimVo = new XpdDimVo();
                    xpdDimVo.setSdDimId(xpdDim.getSdDimId());
                    xpdDimVo.setDimType(xpdDim.getDimType());

                    DimensionList4Get baseDim = baseDimMap.get(xpdDim.getSdDimId());
                    if (baseDim != null) {
                        xpdDimVo.setSdDimName(baseDim.getDmName());
                        xpdDimVo.setSdDimNameI18n(baseDim.getNameI18n());
                    }
                    return xpdDimVo;
                })
                .collect(Collectors.toList());

        i18nTranslator.translate(orgId, userCache.getLocale(), xpdDimVos);
        return xpdDimVos;
    }

    /**
     * 获取盘点项目绑定的维度列表（分页）
     *
     * @param request     HTTP请求
     * @param xpdId       项目ID
     * @param excludePerf 0-不过滤绩效维度 1-过滤绩效维度
     * @param onlyRuleDim 是否只返回规则中绑定的维度（0-否 1-是）
     * @return 分页维度列表
     */
    public PagingList<XpdDim4Get> getXpdDimPage(HttpServletRequest request, String xpdId, Integer excludePerf,
            int onlyRuleDim, String searchKey) {
        if (onlyRuleDim == 1) {
            return getXpdRuleDimPage(request, xpdId, excludePerf, searchKey);
        } else {
            return getXpdModelDimPage(request, xpdId, excludePerf, searchKey);
        }
    }

    /**
     * 返回项目模型中的所有维度
     *
     * @param request
     * @param xpdId
     * @param excludePerf
     * @return
     */
    private PagingList<XpdDim4Get> getXpdModelDimPage(HttpServletRequest request, String xpdId, Integer excludePerf,
        String searchKey) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail(request);
        String orgId = userCacheDetail.getOrgId();

        XpdPO xpd = xpdMapper.selectById(xpdId);
        Validate.isNotNull(xpd, ExceptionKeys.XPD_NOT_EXIST);

        List<ModelDimDTO> modelDimInfos = spsdAclService.getModelDimInfo(orgId, xpd.getModelId());
        if (excludePerf == 1) {
            modelDimInfos.removeIf(ModelDimDTO::isPerfDim);
        }
        if (StringUtils.isNotEmpty(searchKey)) {
            modelDimInfos = modelDimInfos.stream().filter(x -> x.getDimName().contains(searchKey)).toList();
        }

        List<XpdDim4Get> xpdDim4Gets = BeanCopierUtil.convertList(
                modelDimInfos, data -> {
                    XpdDim4Get xpdDim4Get = new XpdDim4Get();
                    xpdDim4Get.setId(data.getDimId());
                    xpdDim4Get.setOrgId(orgId);
                    xpdDim4Get.setName(data.getDimName());
                    xpdDim4Get.setDimid(
                            ApassEntityUtils.createAmSlDrawer4RespDTO(
                                    data.getDimName(), data.getDimId(), data.getDimId(),
                                    data.getDimName()));
                    return xpdDim4Get;
                });

        PageRequest pageRequest = ApiUtil.getPageRequest(request);

        int total = xpdDim4Gets.size();
        long current = pageRequest.getCurrent();
        long size = pageRequest.getSize();
        int fromIndex = (int) ((current - 1) * size);
        int toIndex = (int) Math.min(fromIndex + size, total);
        List<XpdDim4Get> pageData;
        if (fromIndex < total) {
            pageData = xpdDim4Gets.subList(fromIndex, toIndex);
        } else {
            pageData = Collections.emptyList();
        }
        IPage<XpdDim4Get> page = new Page<>(current, size);
        page.setTotal(total);
        page.setRecords(pageData);
        return BeanCopierUtil.toPagingList(page);
    }

    /**
     * 返回盘点项目中绑定的维度列表
     *
     * @param request
     * @param xpdId
     * @param excludePerf
     * @return
     */
    @Nonnull
    private PagingList<XpdDim4Get> getXpdRuleDimPage(HttpServletRequest request, String xpdId, Integer excludePerf,
        String searchKey) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        String orgId = userCacheDetail.getOrgId();
        List<XpdDimPO> dimPOS = xpdDimMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(dimPOS)) {
            return new PagingList<>(new ArrayList<>(), new Paging());
        }
        List<String> searchDimIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(searchKey)) {
            List<String> dimIds = dimPOS.stream().map(XpdDimPO::getSdDimId).toList();
            XpdPO xpdPO = xpdMapper.selectById(xpdId);
            List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, xpdPO.getModelId(), dimIds);
            searchDimIds =
                dimInfoList.stream().filter(x -> x.getDmName().contains(searchKey)).map(ModelBase4Facade::getDmId).toList();
            if (CollectionUtils.isEmpty(searchDimIds)) {
                int offset = (int) ((pageRequest.getCurrent() - 1) * pageRequest.getSize());
                return new PagingList<>(new ArrayList<>(), new Paging(pageRequest.getSize(), offset, 0, 0));
            }

        }


        IPage<XpdDimPO> pageList = xpdDimMapper.selectByXpdIdPage(ApiUtil.toPage(request), orgId, xpdId, excludePerf, searchDimIds);
        List<XpdDimPO> records = pageList.getRecords();

        // 更新维度名称信息
        Map<String, DimensionList4Get> baseDimMap = getSdDimBaseInfo(orgId, records);
        records.forEach(xpdDim -> {
            DimensionList4Get baseDim = baseDimMap.get(xpdDim.getSdDimId());
            if (baseDim != null) {
                xpdDim.setSdDimName(baseDim.getDmName());
                xpdDim.setSdDimNameI18n(baseDim.getNameI18n());
            }
        });

        i18nTranslator.translate(orgId, userCacheDetail.getLocale(), records);

        return BeanCopierUtil.toPagingList(
                pageList, data -> {
                    XpdDim4Get xpdDim4Get = new XpdDim4Get();
                    xpdDim4Get.setId(data.getSdDimId());
                    xpdDim4Get.setOrgId(data.getOrgId());
                    xpdDim4Get.setName(data.getSdDimName());
                    xpdDim4Get.setDimid(
                            ApassEntityUtils.createAmSlDrawer4RespDTO(
                                    data.getSdDimName(), data.getSdDimId(), data.getSdDimId(),
                                    data.getSdDimName()));
                    return xpdDim4Get;
                });
    }

    /**
     * 获取维度基础信息
     *
     * @param orgId      组织ID
     * @param xpdDimList 维度列表
     * @return 维度基础信息Map
     */
    private Map<String, DimensionList4Get> getSdDimBaseInfo(String orgId, List<XpdDimPO> xpdDimList) {
        List<String> sdDimIds = StreamUtil.mapList(xpdDimList, XpdDimPO::getSdDimId);
        List<DimensionList4Get> baseDimDetail = sptalentsdFacade.getBaseDimDetail(orgId, sdDimIds);
        return baseDimDetail.stream()
                .collect(Collectors.toMap(DimensionList4Get::getId, Function.identity()));
    }

    public List<String> listXpdIndicatorUsed(UserCacheDetail userCache, String xpdId) {
        String orgId = userCache.getOrgId();
        List<XpdDimRuleCalcPO> xpdDimRuleCalcList = xpdDimRuleCalcMapper.selectByOrgIdAndXpdId(orgId, xpdId);
        return StreamUtil.mapList(xpdDimRuleCalcList, XpdDimRuleCalcPO::getSdIndicatorId);
    }

    public boolean isXpdRuleExists(String orgId, String xpdId) {
        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        return xpdRuleConfPO != null;
    }

    /**
     * 生成维度组合
     * 1. 自动生成维度组合，两两组合维度
     * 2. 如果维度组合已存在，不再重复创建
     * 3. 如果维度组合不存在，创建新的维度组合
     * 4. 处理默认显示逻辑
     * 5. 移除包含非盘点维度的组合
     *
     * @param orgId  机构ID
     * @param userId 用户ID
     * @param xpdId  项目ID
     * @param gridId 宫格ID
     */
    private void generateDimCombs(String orgId, String userId, String xpdId, String gridId) {
        log.debug("LOG20373:开始生成维度组合: xpdId={}, gridId={}", xpdId, gridId);

        // 获取项目下所有维度
        List<XpdDimPO> allDims = xpdDimMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(allDims) || allDims.size() < 2) {
            log.debug("LOG20363:维度数量不足，无法生成维度组合");
            return;
        }

        // 获取维度ID列表
        List<String> sdDimIds = StreamUtil.mapList(allDims, XpdDimPO::getSdDimId);

        // 获取维度详细信息
        List<DimensionList4Get> dimDetails = spsdAclService.getBaseDimDetail(orgId, sdDimIds);
        Map<String, DimensionList4Get> dimDetailMap = StreamUtil.list2map(dimDetails, DimensionList4Get::getId);

        // 获取现有的维度组合
        List<XpdDimCombPO> existingDimCombs = xpdDimCombMapper.selectByDimIds(orgId, sdDimIds);

        // 创建一个集合来存储已存在的维度对（不区分顺序）
        Set<String> existingDimPairs = getExistingDimPairs(existingDimCombs);

        // 获取当前宫格下所有的维度组合关系
        List<XpdGridDimCombPO> gridDimCombs = xpdGridDimCombMapper.listByXpdIdAndGridId(orgId, xpdId, gridId);

        // 查找默认显示的组合
        DefaultDisplayDimCombInfo defaultDisplayDimCombInfo = findDefaultDisplayInfo(gridDimCombs);

        // 生成维度对并创建新的维度组合
        DimCombGenResult result = generateDimensionPairs(
                orgId, userId, xpdId, gridId, sdDimIds, dimDetailMap,
                existingDimPairs, existingDimCombs, gridDimCombs);

        // 处理默认显示逻辑
        handleDimCombDefaultDisplay(defaultDisplayDimCombInfo, result, sdDimIds, existingDimCombs);

        // 保存新的维度组合和宫格维度组合关系
        saveNewDimCombs(result.getNewDimCombs(), result.getNewGridDimCombs());

        // 移除包含非盘点维度的组合
        removeNonProjectDimCombs(orgId, userId, xpdId, gridId, sdDimIds);

        // 如果是按维度组合配置，则需要为新生成的维度组合生成对应的宫格格子配置
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        if (xpdGrid.getConfigType() == 1) {
            createNewGridCellsForDimCombs(orgId, userId, xpdId, gridId);
        }

        List<String> dimCombNames = StreamUtil.mapList(result.getNewDimCombs(), XpdDimCombPO::getCombName);
        log.debug("LOG20383:维度组合生成完成: 新增维度组合名称: {}", dimCombNames);
    }

    /**
     * 为新生成的维度组合创建宫格格子
     *
     * @param orgId       机构ID
     * @param userId      用户ID
     * @param xpdId       项目ID
     * @param gridId      宫格ID
     */
    private void createNewGridCellsForDimCombs(String orgId, String userId, String xpdId, String gridId) {
        List<XpdGridDimCombPO> xpdGridDimCombs = xpdGridDimCombMapper.listByXpdIdAndGridId(orgId, xpdId, gridId);

        // 查询格子数据
        List<XpdGridCellPO> xpdGridCells = xpdGridCellMapper.listByGridId(orgId, gridId);
        if (CollectionUtils.isEmpty(xpdGridCells)) {
            log.warn("LOG20553:");
            return;
        }
        Map<String, List<XpdGridCellPO>> gridCellListMap =
            xpdGridCells.stream().collect(Collectors.groupingBy(XpdGridCellPO::getDimCombId));

        // 按照维度组合ID进行分组
        List<XpdGridCellPO> gridCells = xpdGridCells.stream()
            .collect(Collectors.groupingBy(XpdGridCellPO::getDimCombId)).values().iterator().next();

        List<XpdGridCellPO> newGridCells = new ArrayList<>();
        for (XpdGridDimCombPO dimComb : xpdGridDimCombs) {
            List<XpdGridCellPO> xpdGridCellPOS = gridCellListMap.get(dimComb.getDimCombId());
            if (CollectionUtils.isEmpty(xpdGridCellPOS)) {
                for (XpdGridCellPO tempCell : gridCells) {
                    // 创建新的宫格格子
                    XpdGridCellPO gridCell = new XpdGridCellPO();
                    BeanHelper.copyProperties(tempCell, gridCell);
                    gridCell.setId(ApiUtil.getUuid());
                    gridCell.setOrgId(orgId);
                    gridCell.setXpdId(xpdId);
                    gridCell.setGridId(gridId);
                    gridCell.setDimCombId(dimComb.getDimCombId());
                    gridCell.setCellDesc(null);
                    EntityUtil.setAuditFields(gridCell);
                    newGridCells.add(gridCell);
                }
            } else {
                for (XpdGridCellPO tempCell : xpdGridCellPOS) {
                    // 创建新的宫格格子
                    XpdGridCellPO gridCell = new XpdGridCellPO();
                    BeanHelper.copyProperties(tempCell, gridCell);
                    gridCell.setId(ApiUtil.getUuid());
                    gridCell.setOrgId(orgId);
                    gridCell.setXpdId(xpdId);
                    gridCell.setGridId(gridId);
                    gridCell.setDimCombId(dimComb.getDimCombId());
                    EntityUtil.setAuditFields(gridCell);
                    newGridCells.add(gridCell);
                }
            }

        }

        // 删除旧的宫格格子
        xpdGridCellMapper.deleteByGridId(orgId, gridId, userId);

        // 批量插入新的宫格格子
        if (CollectionUtils.isNotEmpty(newGridCells)) {
            xpdGridCellMapper.batchInsert(newGridCells);
        }
    }

    /**
     * 移除包含非盘点维度的组合
     *
     * @param orgId    机构ID
     * @param userId   用户ID
     * @param xpdId    项目ID
     * @param gridId   宫格ID
     * @param sdDimIds 项目维度ID列表
     */
    private void removeNonProjectDimCombs(String orgId, String userId, String xpdId, String gridId,
            List<String> sdDimIds) {
        log.debug("LOG20413:开始移除包含非盘点维度的组合: xpdId={}, gridId={}", xpdId, gridId);

        // 获取当前宫格下所有的维度组合关系
        List<XpdGridDimCombPO> gridDimCombs = xpdGridDimCombMapper.listByXpdIdAndGridId(orgId, xpdId, gridId);
        if (CollectionUtils.isEmpty(gridDimCombs)) {
            return;
        }

        // 收集需要移除的宫格维度组合关系ID
        List<String> gridDimCombIdsToRemove = new ArrayList<>();
        boolean defaultDisplayWillBeRemoved = false;

        // 遍历所有宫格维度组合关系
        for (XpdGridDimCombPO gridDimComb : gridDimCombs) {
            String dimCombId = gridDimComb.getDimCombId();
            XpdDimCombPO dimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);

            // 如果维度组合不存在，则跳过
            if (dimComb == null) {
                continue;
            }

            // 检查维度组合中的维度是否都在项目维度列表中
            String xSdDimId = dimComb.getXSdDimId();
            String ySdDimId = dimComb.getYSdDimId();

            // 如果维度组合中有任一维度不在项目维度列表中，则需要移除
            if (!sdDimIds.contains(xSdDimId) || !sdDimIds.contains(ySdDimId)) {
                gridDimCombIdsToRemove.add(gridDimComb.getId());

                // 检查是否移除的是默认显示的组合
                if (gridDimComb.getShowType() != null && gridDimComb.getShowType() == 1) {
                    log.debug("LOG20453:默认显示的维度组合将被移除: dimCombId={}", dimCombId);
                    defaultDisplayWillBeRemoved = true;
                }
            }
        }

        // 批量删除需要移除的宫格维度组合关系
        if (!gridDimCombIdsToRemove.isEmpty()) {
            xpdGridDimCombMapper.deleteByIds(gridDimCombIdsToRemove, userId);
            log.debug("LOG20403:已移除包含非盘点维度的组合: ids={}", gridDimCombIdsToRemove);

            // 如果默认显示的组合被移除，需要设置一个新的默认显示
            if (defaultDisplayWillBeRemoved) {
                setNewDefaultDisplay(orgId, xpdId, gridId, userId);
            }
        } else {
            log.debug("LOG20393:没有需要移除的非盘点维度组合");
        }
    }

    /**
     * 设置新的默认显示维度组合
     * 当默认显示的维度组合被删除时，自动设置一个新的维度组合为默认显示
     *
     * @param orgId  机构ID
     * @param xpdId  项目ID
     * @param gridId 宫格ID
     * @param userId 用户ID
     */
    private void setNewDefaultDisplay(String orgId, String xpdId, String gridId, String userId) {
        log.debug("LOG20463:开始设置新的默认显示维度组合");

        // 获取当前宫格下所有的维度组合关系（此时已经删除了不符合条件的组合）
        List<XpdGridDimCombPO> gridDimCombs = xpdGridDimCombMapper.listByXpdIdAndGridId(orgId, xpdId, gridId);
        if (CollectionUtils.isEmpty(gridDimCombs)) {
            log.debug("LOG20473:没有可用的维度组合，无法设置默认显示");
            return;
        }

        // 检查是否已有默认显示的维度组合
        for (XpdGridDimCombPO gridDimComb : gridDimCombs) {
            if (gridDimComb.getShowType() != null && gridDimComb.getShowType() == 1) {
                log.debug("LOG20483:已存在默认显示的维度组合，无需重新设置");
                return;
            }
        }

        // 设置第一个维度组合为默认显示
        XpdGridDimCombPO firstGridDimComb = gridDimCombs.get(0);
        firstGridDimComb.setShowType(ShowTypeEnum.YES.getCode());
        firstGridDimComb.setUpdateTime(LocalDateTime.now());
        firstGridDimComb.setUpdateUserId(userId);
        xpdGridDimCombMapper.insertOrUpdate(firstGridDimComb);

        log.debug("LOG20493:已设置新的默认显示维度组合: dimCombId={}", firstGridDimComb.getDimCombId());
    }

    /**
     * 获取已存在的维度对集合
     */
    private Set<String> getExistingDimPairs(List<XpdDimCombPO> existingDimCombs) {
        Set<String> existingDimPairs = new HashSet<>();
        for (XpdDimCombPO dimComb : existingDimCombs) {
            String dimPair = getDimPairKey(dimComb.getXSdDimId(), dimComb.getYSdDimId());
            existingDimPairs.add(dimPair);
        }
        return existingDimPairs;
    }

    /**
     * 查找默认显示的组合信息
     */
    private DefaultDisplayDimCombInfo findDefaultDisplayInfo(List<XpdGridDimCombPO> gridDimCombs) {
        DefaultDisplayDimCombInfo info = new DefaultDisplayDimCombInfo();
        for (XpdGridDimCombPO gridDimComb : gridDimCombs) {
            if (gridDimComb.getShowType() != null && gridDimComb.getShowType() == 1) {
                info.setHasDefaultDisplay(true);
                info.setDefaultDisplayDimCombId(gridDimComb.getDimCombId());
                break;
            }
        }
        return info;
    }

    /**
     * 生成维度对并创建新的维度组合
     */
    private DimCombGenResult generateDimensionPairs(
            String orgId, String userId, String xpdId, String gridId,
            List<String> sdDimIds, Map<String, DimensionList4Get> dimDetailMap,
            Set<String> existingDimPairs, List<XpdDimCombPO> existingDimCombs,
            List<XpdGridDimCombPO> gridDimCombs) {

        List<XpdDimCombPO> newDimCombs = new ArrayList<>();
        List<XpdGridDimCombPO> newGridDimCombs = new ArrayList<>();
        String firstGeneratedDimCombId = null;

        // 两两组合维度
        for (int i = 0; i < sdDimIds.size(); i++) {
            for (int j = i + 1; j < sdDimIds.size(); j++) {
                String xSdDimId = sdDimIds.get(i);
                String ySdDimId = sdDimIds.get(j);

                // 使用字典序排列维度ID，确保A-B和B-A被视为相同的组合
                String dimPairKey = getDimPairKey(xSdDimId, ySdDimId);

                if (!existingDimPairs.contains(dimPairKey)) {
                    // 如果维度组合不存在，则创建新的维度组合
                    String newDimCombId = createNewDimComb(
                            orgId, userId, xpdId, gridId, dimDetailMap,
                            xSdDimId, ySdDimId, newDimCombs, newGridDimCombs);

                    if (firstGeneratedDimCombId == null) {
                        firstGeneratedDimCombId = newDimCombId;
                    }

                    existingDimPairs.add(dimPairKey);
                } else {
                    // 如果维度组合已存在，检查是否需要创建宫格维度组合关系
                    String existingDimCombId = handleExistingDimComb(
                            orgId, userId, xpdId, gridId,
                            existingDimCombs, gridDimCombs, dimPairKey, newGridDimCombs);

                    if (firstGeneratedDimCombId == null && existingDimCombId != null) {
                        firstGeneratedDimCombId = existingDimCombId;
                    }
                }
            }
        }

        DimCombGenResult result = new DimCombGenResult();
        result.setNewDimCombs(newDimCombs);
        result.setNewGridDimCombs(newGridDimCombs);
        result.setFirstGeneratedDimCombId(firstGeneratedDimCombId);

        return result;
    }

    /**
     * 创建新的维度组合
     */
    private String createNewDimComb(
            String orgId, String userId, String xpdId, String gridId,
            Map<String, DimensionList4Get> dimDetailMap,
            String xSdDimId, String ySdDimId,
            List<XpdDimCombPO> newDimCombs, List<XpdGridDimCombPO> newGridDimCombs) {

        // 获取维度名称
        String xSdDimName = dimDetailMap.getOrDefault(xSdDimId, new DimensionList4Get()).getDmName();
        String ySdDimName = dimDetailMap.getOrDefault(ySdDimId, new DimensionList4Get()).getDmName();
        String combName = xSdDimName + "-" + ySdDimName;

        // 创建维度组合
        XpdDimCombPO newDimComb = XpdPOFactory.buildXpdDimComb(orgId, userId, combName, xSdDimId, ySdDimId);
        newDimCombs.add(newDimComb);

        // 创建宫格维度组合关系
        XpdGridDimCombPO newGridDimComb = createGridDimComb(
                orgId, userId, xpdId, gridId, newDimComb.getId(), newGridDimCombs.size());
        newGridDimCombs.add(newGridDimComb);

        return newDimComb.getId();
    }

    /**
     * 处理已存在的维度组合
     */
    @Nullable
    private String handleExistingDimComb(
            String orgId, String userId, String xpdId, String gridId,
            List<XpdDimCombPO> existingDimCombs, List<XpdGridDimCombPO> gridDimCombs,
            String dimPairKey, List<XpdGridDimCombPO> newGridDimCombs) {

        // 找到对应的维度组合ID
        String existingDimCombId = findExistingDimCombId(existingDimCombs, dimPairKey);
        if (existingDimCombId == null) {
            return null;
        }

        // 检查是否已经存在宫格维度组合关系
        boolean gridDimCombExists = checkGridDimCombExists(gridDimCombs, existingDimCombId);

        // 如果不存在宫格维度组合关系，则创建
        if (!gridDimCombExists) {
            XpdGridDimCombPO newGridDimComb = createGridDimComb(
                    orgId, userId, xpdId, gridId, existingDimCombId, newGridDimCombs.size());
            newGridDimCombs.add(newGridDimComb);
            return existingDimCombId;
        }

        return null;
    }

    /**
     * 查找已存在的维度组合ID
     */
    @Nullable
    private String findExistingDimCombId(List<XpdDimCombPO> existingDimCombs, String dimPairKey) {
        for (XpdDimCombPO dimComb : existingDimCombs) {
            if (getDimPairKey(dimComb.getXSdDimId(), dimComb.getYSdDimId()).equals(dimPairKey)) {
                return dimComb.getId();
            }
        }
        return null;
    }

    /**
     * 检查宫格维度组合关系是否存在
     */
    private boolean checkGridDimCombExists(List<XpdGridDimCombPO> gridDimCombs, String dimCombId) {
        for (XpdGridDimCombPO gridDimComb : gridDimCombs) {
            if (gridDimComb.getDimCombId().equals(dimCombId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建宫格维度组合关系
     */
    private XpdGridDimCombPO createGridDimComb(
            String orgId, String userId, String xpdId, String gridId,
            String dimCombId, int orderIndex) {

        XpdGridDimCombPO gridDimComb = new XpdGridDimCombPO();
        gridDimComb.setId(ApiUtil.getUuid());
        gridDimComb.setOrgId(orgId);
        gridDimComb.setXpdId(xpdId);
        gridDimComb.setGridId(gridId);
        gridDimComb.setDimCombId(dimCombId);
        gridDimComb.setShowType(0); // 默认不显示
        gridDimComb.setDeleted(0);
        gridDimComb.setOrderIndex(orderIndex);
        gridDimComb.setCreateTime(LocalDateTime.now());
        gridDimComb.setCreateUserId(userId);
        gridDimComb.setUpdateTime(LocalDateTime.now());
        gridDimComb.setUpdateUserId(userId);

        return gridDimComb;
    }

    /**
     * 处理默认显示逻辑
     */
    private void handleDimCombDefaultDisplay(
            DefaultDisplayDimCombInfo defaultInfo, DimCombGenResult result,
            List<String> sdDimIds, List<XpdDimCombPO> existingDimCombs) {

        if (!defaultInfo.isHasDefaultDisplay() && result.getFirstGeneratedDimCombId() != null) {
            // 如果当前没有默认显示的组合，将第一个自动生成的维度组合设为默认显示
            setFirstDimCombAsDefault(result.getFirstGeneratedDimCombId(), result.getNewGridDimCombs());
        } else if (defaultInfo.isHasDefaultDisplay()) {
            // 检查默认显示的组合是否仍然存在
            boolean defaultDimCombStillExists = checkDefaultDimCombExists(
                    defaultInfo.getDefaultDisplayDimCombId(), sdDimIds, existingDimCombs);

            // 如果默认显示的组合不再存在，将第一个自动生成的维度组合设为默认显示
            if (!defaultDimCombStillExists && result.getFirstGeneratedDimCombId() != null) {
                setFirstDimCombAsDefault(result.getFirstGeneratedDimCombId(), result.getNewGridDimCombs());
            }
        }
    }

    /**
     * 设置第一个组合为默认显示
     */
    private void setFirstDimCombAsDefault(String dimCombId, List<XpdGridDimCombPO> gridDimCombs) {
        for (XpdGridDimCombPO gridDimComb : gridDimCombs) {
            if (gridDimComb.getDimCombId().equals(dimCombId)) {
                gridDimComb.setShowType(ShowTypeEnum.YES.getCode());
                break;
            }
        }
    }

    /**
     * 检查默认组合是否仍然存在
     */
    private boolean checkDefaultDimCombExists(
            String defaultDimCombId, List<String> sdDimIds, List<XpdDimCombPO> dimCombs) {

        for (XpdDimCombPO dimComb : dimCombs) {
            if (dimComb.getId().equals(defaultDimCombId)) {
                return sdDimIds.contains(dimComb.getXSdDimId()) && sdDimIds.contains(dimComb.getYSdDimId());
            }
        }
        return false;
    }

    /**
     * 保存新的维度组合和宫格维度组合关系
     */
    private void saveNewDimCombs(List<XpdDimCombPO> newDimCombs, List<XpdGridDimCombPO> newGridDimCombs) {
        if (CollectionUtils.isNotEmpty(newDimCombs)) {
            xpdDimCombMapper.batchInsert(newDimCombs);
        }

        if (CollectionUtils.isNotEmpty(newGridDimCombs)) {
            xpdGridDimCombMapper.batchInsert(newGridDimCombs);
        }
    }

    /**
     * 获取维度对的唯一标识，使用字典序排列维度ID
     *
     * @param dimId1 维度ID1
     * @param dimId2 维度ID2
     * @return 维度对的唯一标识
     */
    private String getDimPairKey(String dimId1, String dimId2) {
        // 使用字典序排列维度ID，确保A-B和B-A被视为相同的组合
        if (dimId1.compareTo(dimId2) <= 0) {
            return dimId1 + "-" + dimId2;
        } else {
            return dimId2 + "-" + dimId1;
        }
    }

    /**
     * 维度组的默认显示信息 信息搜集类
     */
    @Setter
    @Getter
    private static class DefaultDisplayDimCombInfo {
        private boolean hasDefaultDisplay;
        private String defaultDisplayDimCombId;

    }

    /**
     * 维度组合生成结果类
     */
    @Setter
    @Getter
    private static class DimCombGenResult {
        private List<XpdDimCombPO> newDimCombs;
        private List<XpdGridDimCombPO> newGridDimCombs;
        private String firstGeneratedDimCombId;

    }
}
