package com.yxt.talent.rv.application.perf.lagecy;

import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.application.perf.dto.PerfGradeDTO;
import com.yxt.talent.rv.controller.manage.perf.command.PerfGradeCmd;
import com.yxt.talent.rv.controller.manage.perf.command.PerfGradeSortCmd;
import com.yxt.talent.rv.domain.perf.PerfGrade;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PerfGradeAppService {

    public static final int MAX_LEASE_TIME = 120;
    private final PerfGradeMapper perfGradeMapper;
    private final ILock lockService;

    private final PerfMapper perfMapper;



    /**
     * 获取机构下的自定义绩效等级数据（包含第一次初始化动作）
     *
     * @param orgId
     * @param userId
     */
    @DbHintMaster
    public List<PerfGradeDTO> getOrgPerfGradeList(
            String orgId, String userId, @Nullable Integer state) {
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgIdAll(orgId);
        if (CollectionUtils.isEmpty(perfGrades)) {
            // 自定义绩效一定有数据，查不到做默认绩效等级初始化
            // 加锁防止并发
            List<PerfGradePO> perfGradeList = new ArrayList<>();
            String lockKey = String.format(RedisKeys.LK_ORG_PERF_GRADE_INIT, orgId);
            if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 排序从1开始
                    int sort = 1;

                    for (Map.Entry<String, Integer> entry : PerfGrade.DefaultLevel.findNameCodeMap().entrySet()) {
                        PerfGradePO perfGrade = new PerfGradePO();
                        perfGrade.setId(ApiUtil.getUuid());
                        perfGrade.setOrgId(orgId);
                        perfGrade.setGradeName(entry.getKey());
                        perfGrade.setGradeValue(entry.getValue());
                        perfGrade.setDeleted(0);
                        perfGrade.setState(1);
                        EntityUtil.setAuditFields(perfGrade,userId);
                        perfGradeList.add(perfGrade);
                    }

                    if (CollectionUtils.isNotEmpty(perfGradeList)) {
                        List<PerfGradePO> result = perfGradeList.stream()
                                .sorted(Comparator.comparing(PerfGradePO::getGradeValue))
                                .toList();
                        for (PerfGradePO perfGradePO : result) {
                            perfGradePO.setOrderIndex(sort++);
                        }
                        perfGradeMapper.batchInsertOrUpdate(result);
                        perfGradeList = result;
                    }
                } catch (Exception e) {
                    log.error("LOG42316:初始化机构默认自定义绩效等级数据出错：orgId={}", orgId, e);
                    throw e;
                } finally {
                    lockService.unLock(lockKey);
                }
            }
            // 将插入的数据做返回
            perfGrades.addAll(perfGradeList);
        }
        // 过滤掉已经删除绩效等级

        List<PerfGradePO> results = new ArrayList<>();
        for (PerfGradePO perfGrade : perfGrades) {
            if (perfGrade.getDeleted() == 1) {
                continue;
            }
            // 过滤掉禁用的
            if (state != null && state == 1 && perfGrade.getState() == 0) {
                continue;
            }
            results.add(perfGrade);
        }
        // 转化，返回
        return BeanCopierUtil.convertList(new ArrayList<>(results), PerfGradePO.class, PerfGradeDTO.class);
    }

    /**
     * 等级名称校验
     *
     * @param orgId
     * @param perfGradeCmd
     */
    public void validateName(String orgId, PerfGradeCmd perfGradeCmd) {
        // 重名校验
        long check = perfGradeMapper.countByGradeName(orgId, perfGradeCmd.getGradeName(),
                perfGradeCmd.getId());
        Validate.isTrue(check == 0, ExceptionKeys.PERF_GRADE_NAME_CONFLICT);
    }

    /**
     * 创建/更新自定义绩效等级
     *
     * @param orgId
     * @param userId
     * @param perfGradeCmd
     */
    public String handleGrade(
            String orgId, String userId, PerfGradeCmd perfGradeCmd) {
        // 重名校验
        long check = perfGradeMapper.countByGradeName(orgId, perfGradeCmd.getGradeName(),
                perfGradeCmd.getId());
        Validate.isTrue(check == 0, ExceptionKeys.PERF_GRADE_NAME_CONFLICT);
        PerfGradePO perfGrade = new PerfGradePO();
        String lockKey = String.format(RedisKeys.LK_ORG_PERF_GRADE_OP, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                // 判断新增或者更新
                if (StringUtils.isEmpty(perfGradeCmd.getId())) {
                    // 新增数量校验，最多30个
                    /*long count = perfGradeMapper.countByOrgId(orgId);
                    Validate.isTrue(count <= 49, ExceptionKeys.PERF_GRADE_LIMIT);*/
                    perfGrade.setId(ApiUtil.getUuid());
                    perfGrade.setOrgId(orgId);
                    EntityUtil.setAuditFields(perfGrade, userId);
                    perfGrade.setGradeName(perfGradeCmd.getGradeName());
                    perfGrade.setDeleted(DeleteEnum.NOT_DELETED.getCode());
                    // 处理排序值
                    int currentMaxSort = perfGradeMapper.currentMaxSort(orgId);
                    int currentOrderIndex =
                            perfGradeMapper.selectMaxOrderIndex(orgId);
                    perfGrade.setGradeValue(currentMaxSort + 1);
                    perfGrade.setOrderIndex(currentOrderIndex + 1);
                } else {
                    // 更新
                    perfGrade = perfGradeMapper.selectByOrgIdAndId(orgId, perfGradeCmd.getId());
                    perfGrade.setGradeName(perfGradeCmd.getGradeName());
                    EntityUtil.setUpdate(perfGrade, userId);
                }
                perfGradeMapper.insertOrUpdateBatch(List.of(perfGrade));
            } catch (Exception e) {
                log.error("LOG62690:绩效等级新增/编辑出错：orgId={}", orgId, e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        }
        return perfGrade.getId();
    }

    /**
     * 绩效等级拖拽排序
     *
     * @param orgId
     * @param userId
     * @param gradeSort
     */
    public void sort(String orgId, String userId, PerfGradeSortCmd gradeSort) {
        // 查询变更的绩效周期数据
        PerfGradePO perfGrade = perfGradeMapper.selectByOrgIdAndId(orgId, gradeSort.getId());
        Validate.isNotNull(perfGrade, ExceptionKeys.PERF_GRADE_NOT_EXIST);
        String lockKey = String.format(RedisKeys.LK_ORG_PERF_GRADE_OP, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                Collection<PerfGradePO> perfGradePOS = perfGradeMapper.selectByOrgId(orgId);
                int oldSort = -1;
                int temp = 1;
                for (PerfGradePO perfGradePO : perfGradePOS) {
                    if (perfGradePO.getId().equals(perfGrade.getId())) {
                        oldSort = temp;
                    }
                    temp++;
                }
                int newSort = gradeSort.getSort();
                // 判断新旧两个排序之间的差异，如果新旧排序无差异，则不做任何处理
                if (oldSort == newSort) {
                    return;
                }
                // 计算出位移
                int move;
                if (oldSort > newSort) {
                    // 上移动
                    move = 1;
                } else {
                    // 下移
                    move = -1;
                }
                // 所有数据排序+1或者-1
                List<PerfGradePO> resultList = new ArrayList<>();
                int index = 1;
                getResultList(userId, perfGrade, perfGradePOS, newSort, move, resultList, index);
                if (CollectionUtils.isNotEmpty(resultList)) {
                    perfGradeMapper.insertOrUpdateBatch(resultList);
                }
            } catch (Exception e) {
                log.error("LOG62700:绩效等级排序出错：orgId={}", orgId, e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        }
    }

    private void getResultList(
            String userId, PerfGradePO perfGrade, Collection<PerfGradePO> perfGradePOS, int newSort,
            int move, List<PerfGradePO> resultList, int index) {
        int targetIndex;
        for (PerfGradePO perfGradePO : perfGradePOS) {
            // 向下移动 新顺序后的所有 数据库排序向下移动
            if (move == -1) {
                if (newSort == index) {
                    targetIndex = perfGradePO.getOrderIndex();
                    perfGrade.setOrderIndex(targetIndex + 1);
                    EntityUtil.setUpdate(perfGrade, userId);
                    resultList.add(perfGrade);
                } else if (index > newSort){
                    perfGradePO.setOrderIndex(perfGradePO.getOrderIndex() + 1);
                    EntityUtil.setUpdate(perfGradePO, userId);
                    resultList.add(perfGradePO);
                }
            } else {
                // 向上移动
                if (perfGrade.getId().equals(perfGradePO.getId())) {
                    break;
                }
                if (newSort == index) {
                    targetIndex = perfGradePO.getOrderIndex();
                    perfGrade.setOrderIndex(targetIndex);
                    EntityUtil.setUpdate(perfGrade, userId);
                    resultList.add(perfGrade);

                    perfGradePO.setOrderIndex(targetIndex + 1);
                    EntityUtil.setUpdate(perfGradePO, userId);
                    resultList.add(perfGradePO);
                } else if (index > newSort){
                    perfGradePO.setOrderIndex(perfGradePO.getOrderIndex() + 1);
                    EntityUtil.setUpdate(perfGradePO, userId);
                    resultList.add(perfGradePO);
                }

            }

            index++;
        }
    }

    /**
     * 删除绩效等级
     *
     * @param orgId
     * @param userId
     * @param id
     */
    public void removeGrade(String orgId, String userId, String id) {
        PerfGradePO perfGrade = perfGradeMapper.selectByOrgIdAndId(orgId, id);
        if (perfGrade == null) {
            return;
        }
        // 启用的绩效等级，不允许被删除
        if (perfGrade.getState() == 1) {
            throw new ApiException(ExceptionKeys.PERF_LEVEL_STATE_USED);
        }
        // 检查该绩效等级是否已经被使用
        chkPerfUsed(orgId, perfGrade.getGradeValue());
        String lockKey = String.format(RedisKeys.LK_ORG_PERF_GRADE_INIT, orgId);

        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                perfGrade.setDeleted(DeleteEnum.DELETED.getCode());
                EntityUtil.setUpdate(perfGrade, userId);
                perfGradeMapper.insertOrUpdateBatch(List.of(perfGrade));

            } catch (Exception e) {
                log.error("LOG62710:绩效等级删除出错：orgId={}", orgId, e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        }
    }

    /**
     *
     *
     * @param orgId
     */
    private void chkPerfUsed(String orgId, Integer gradeValue) {
        String periodLevel = perfMapper.findPeriodLevel(orgId, String.valueOf(gradeValue));
        if (StringUtils.isNotBlank(periodLevel)) {
            throw new ApiException(ExceptionKeys.PERF_LEVEL_USED);
        }

        // TODO 检查盘点项目是否使用
    }

    /**
     * 绩效等级，启用，禁用
     *
     * @param orgId
     * @param userId
     * @param id
     * @param state
     */
    public void editGradeState(String orgId, String userId,  String id, Integer state) {
        perfGradeMapper.updateStateById(orgId, id, state, userId);
    }

    public void washGrade(){
        log.info("LOG14425:washGrade start");
        List<String> orgIds = perfGradeMapper.findGradeOrgId();
        for (String orgId : orgIds) {
            log.info("LOG14415:washGrade orgId={}", orgId);
            dealOrgDefault(orgId);
        }

        log.info("LOG14405:washGrade end");
    }

    /**
     * 处理机构默认等级数据
     *
     * @param orgId
     */
    private void dealOrgDefault(String orgId) {
        // 检查此机构是否已经将默认等级初始化
        Collection<PerfGradePO> perfGradePOS = perfGradeMapper.selectByOrgId(orgId);
        for (PerfGradePO perfGradePO : perfGradePOS) {
            if (perfGradePO.getGradeValue() == 1) {
                return;
            }
        }
        // 获取最大排序
        Integer maxIndex =
                perfGradePOS.stream().map(PerfGradePO::getOrderIndex).max(Integer::compareTo)
                        .orElse(0);
        List<PerfGradePO> perfGradeList = getPerfGradePOS(orgId);
        if (CollectionUtils.isNotEmpty(perfGradeList)) {
            List<PerfGradePO> result =
                    perfGradeList.stream().sorted(Comparator.comparing(PerfGradePO::getGradeValue))
                            .toList();
            for (PerfGradePO perfGradePO : result) {
                perfGradePO.setOrderIndex(++maxIndex);
            }
            perfGradeMapper.batchInsertOrUpdate(result);
        }
        log.info("LOG14395:dealOrgDeafault orgId={}", orgId);
    }

    private static @Nonnull List<PerfGradePO> getPerfGradePOS(String orgId) {
        List<PerfGradePO> perfGradeList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : PerfGrade.DefaultLevel.findNameCodeMap().entrySet()) {
            PerfGradePO perfGrade = new PerfGradePO();
            perfGrade.setId(ApiUtil.getUuid());
            perfGrade.setOrgId(orgId);
            perfGrade.setGradeName(entry.getKey());
            perfGrade.setGradeValue(entry.getValue());
            perfGrade.setDeleted(0);
            perfGrade.setState(1);
            EntityUtil.setAuditFields(perfGrade,"init");
            perfGradeList.add(perfGrade);
        }
        return perfGradeList;
    }


}
