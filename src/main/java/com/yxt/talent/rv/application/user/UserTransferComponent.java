package com.yxt.talent.rv.application.user;

import com.yxt.talent.rv.application.user.dto.UserTransferResultMsg;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.BiFunction;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE_CALLBACK;

/**
 * 用户资源转移通用组件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserTransferComponent {

    private final RocketMqAclSender rocketMqAclSender;

    @Transactional(rollbackFor = Exception.class)
    public void transferResource(
            String orgId, String fromUserId, String toUserId,
            @Nonnull BiFunction<String, String, Integer> resourceCnt,
            @Nonnull Runnable transfer, String resourceCode) {
        log.info("LOG67580:orgId={}, fromUserId={}, toUserId={}, resourceCode={}", orgId,
                fromUserId, toUserId, resourceCode);

        UserTransferResultMsg transferResult =
                UserTransferComponent.buildUserTransferResultMsg(orgId, fromUserId,
                        resourceCode);

        int cnt = 0;
        int state = 1;
        String errMsg = "";
        try {
            // 查询名下资源总数
            cnt = resourceCnt.apply(orgId, fromUserId);
            if (cnt == 0) {
                log.debug("LOG67590:");
                return;
            }

            // 开始转移
            transfer.run();
        } catch (Exception e) {
            log.error("LOG67600:", e);
            state = 2;
            errMsg = e.getMessage();
            // 触发事务回滚
            throw e;
        } finally {
            transferResult.setState(state);
            transferResult.setErrorMsg(errMsg);
            transferResult.setSuccessCount(state == 1 ? cnt : 0);
            transferResult.setFailedCount(state == 1 ? 0 : cnt);

            sendCallBack(transferResult);
        }
    }

    private void sendCallBack(UserTransferResultMsg transferResult) {
        rocketMqAclSender.send(
                TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE_CALLBACK,
                bean2Json(transferResult, ALWAYS));
    }

    @Nonnull
    public static UserTransferResultMsg buildUserTransferResultMsg(
            String orgId, String fromUserId, String resourceCode) {
        UserTransferResultMsg transferResult = new UserTransferResultMsg();
        transferResult.setOrgId(orgId);
        transferResult.setUserId(fromUserId);
        transferResult.setResourceCode(resourceCode);
        return transferResult;
    }

    @Transactional(rollbackFor = Exception.class)
    public void transferResource4Xpd(
        String orgId, String fromUserId, String toUserId,
        int cnt,
        @Nonnull Runnable transfer, String resourceCode) {
        log.info("transferResource4Xpd:orgId={}, fromUserId={}, toUserId={}, resourceCode={}", orgId,
            fromUserId, toUserId, resourceCode);

        UserTransferResultMsg transferResult =
            UserTransferComponent.buildUserTransferResultMsg(orgId, fromUserId,
                resourceCode);

        int state = 1;
        String errMsg = "";
        try {
            // 查询名下资源总数
            if (cnt == 0) {
                log.debug("transferResource4Xpd 1:");
                return;
            }

            // 开始转移
            transfer.run();
        } catch (Exception e) {
            log.error("LOG42306:", e);
            state = 2;
            errMsg = e.getMessage();
            // 触发事务回滚
            throw e;
        } finally {
            transferResult.setState(state);
            transferResult.setErrorMsg(errMsg);
            transferResult.setSuccessCount(state == 1 ? cnt : 0);
            transferResult.setFailedCount(state == 1 ? 0 : cnt);

            sendCallBack(transferResult);
        }
    }


}
