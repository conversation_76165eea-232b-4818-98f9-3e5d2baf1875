package com.yxt.talent.rv.application.todo;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import com.yxt.common.Constants;
import com.yxt.msgfacade.bean.GroupUserInfo;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4Modify;
import com.yxt.msgfacade.bean.UserDetail;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationMemberPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 认证项目待办
 */
@Component
public class AuthPrjStrategy extends
        TodoCustomTimeSceneStrategy<AuthPrjStrategy.AuthPrjTodoInfoDto, AuthPrjStrategy.AuthPrjTodoInfoDto> {

    private final String PARAM_PRJ_NAME = "{{certificateName}}";

    private final String PARAM_PRJ_TITLE_NAME = "{{name}}";
    private final String PARAM_PRJ_TITLE_TYPE = "{{typeName}}";
    private final String PARAM_START_TIME = "{{startTime}}";
    private final String PARAM_END_TIME = "{{endTime}}";
    private final String PARAM_URL = "{{url}}";

    private final String CUSTOM_PARAM_NAME = "name";
    private final String CUSTOM_PARAM_BIZID = "bizIdL1";
    private final String CUSTOM_PARAM_TAGI18N = "tagI18n";

    public AuthPrjStrategy() {
    }

    @Override
    public TodoSceneEnum getTodoSceneEnum() {
        return TodoSceneEnum.AUTH_PRJ_START;
    }

    @Override
    protected Todo4Create convert2TodoCreate(String orgId, String opUserId, AuthPrjTodoInfoDto bizParam) {
        Map<String, String> params = getParams(bizParam.getPrjName(), bizParam.getStartTime(),
            bizParam.getEndTime(), bizParam.getUrl());
        Map<String, Object> customParams = getParamsObj(bizParam.getPrjName(), bizParam.getAuthPrjId());
        Todo4Create create = new Todo4Create();
        create.setSceneCode(getSceneCode());
        create.setOrgId(orgId);
        create.setOrganizer(opUserId);
        create.setOperateUserId(opUserId);
        create.setTodoId(bizParam.getAuthPrjId());
        create.setParams(params);
        create.setJumpUrl(bizParam.getUrl());
        create.setCustomParams(customParams);
        GroupUserInfo groupUserInfo = new GroupUserInfo();
        groupUserInfo.setOrgId(orgId);
        groupUserInfo.setUserIds(Lists.newArrayList());
        create.setGroupUserInfos(ListUtil.toList(groupUserInfo));
        create.setStartTime(bizParam.getStartTime());
        create.setEndTime(bizParam.getEndTime());
        return create;
    }

    @Override
    protected List<Todo4Create> convert2TodoCreates(String orgId, String opUserId, AuthPrjTodoInfoDto bizParam) {
        return bizParam.users.stream().map(user -> {
            Map<String, String> params = getParams(bizParam.getPrjName(), bizParam.getStartTime(),
                bizParam.getEndTime(), bizParam.getUrl());
            Map<String, Object> customParams = getParamsObj(bizParam.getPrjName(), bizParam.getAuthPrjId());
            GroupUserInfo groupUserInfo = new GroupUserInfo();
            groupUserInfo.setOrgId(orgId);
            groupUserInfo.setUserIds(ListUtil.toList(user.getUserId()));
            Todo4Create create = new Todo4Create();
            create.setSceneCode(getSceneCode());
            create.setOrgId(orgId);
            create.setOrganizer(opUserId);
            create.setOperateUserId(opUserId);
            create.setTodoId(user.getActvId());
            create.setParams(params);
            create.setGroupUserInfos(ListUtil.toList(groupUserInfo));
            create.setJumpUrl(bizParam.getUrl());
            create.setCustomParams(customParams);
            create.setStartTime(bizParam.getStartTime());
            create.setEndTime(bizParam.getEndTime());
            return create;
        }).collect(Collectors.toList());
    }

    private Map<String, Object> getParamsObj(String authPrjName, String bizId) {
        Map<String, Object> params = new HashMap<>();
        params.put(CUSTOM_PARAM_NAME, authPrjName);
        params.put(CUSTOM_PARAM_BIZID, bizId);
        params.put(CUSTOM_PARAM_TAGI18N, "talent_rv_authprj_i18n_key");
        return params;
    }


    @Override
    protected Todo4Modify convert2TodoInfo4ModifyItem(String orgId, String opUserId, AuthPrjTodoInfoDto bizParam) {
        Todo4Modify todo4Modify = new Todo4Modify();
        //机构id
        todo4Modify.setOrgIds(Collections.singletonList(orgId));
        //操作人
        todo4Modify.setOperateUserId(opUserId);
        //待办id
        todo4Modify.setTodoId(bizParam.getAuthPrjId());
        //场景code
        todo4Modify.setSceneCode(getSceneCode());
        //跳转url
        todo4Modify.setJumpUrl(bizParam.getUrl());
        todo4Modify.setUnModifyTimeFlag(true);
        if (null != bizParam.getStartTime()) {
            todo4Modify.setStartTime(bizParam.getStartTime());
        }
        if (null != bizParam.getEndTime()) {
            todo4Modify.setEndTime(bizParam.getEndTime());
        }
        //自定义参数-模板替换
        Map<String, Object> customParams = getParamsObj(bizParam.getPrjName(), bizParam.getAuthPrjId());
        todo4Modify.setCustomParams(customParams);
        Map<String, String> params = getParams(bizParam.getPrjName(), bizParam.getStartTime(), bizParam.getEndTime(),
                bizParam.getUrl());
        todo4Modify.setParams(params);
        return todo4Modify;
    }


    private Map<String, String> getParams(String prjName, LocalDateTime startTime, LocalDateTime endTime,
            String jumpUrl) {
        Map<String, String> params = new HashMap<>();
        params.put(PARAM_PRJ_NAME, prjName);
        if (Objects.nonNull(startTime)) {
            params.put(PARAM_START_TIME, DateTimeFormatter.ofPattern(Constants.SDF_YEAR2MINUTE).format(startTime));
        }
        if (Objects.nonNull(endTime)) {
            params.put(PARAM_END_TIME, DateTimeFormatter.ofPattern(Constants.SDF_YEAR2MINUTE).format(endTime));
        }
        params.put(PARAM_URL, jumpUrl);
        return params;
    }

    @Override
    protected List<UserDetail> convertUser(AuthPrjTodoInfoDto bizParam) {
        List<RvActivityParticipationMemberPO> users = bizParam.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyList();
        }
        return users.stream().map(user -> {
            UserDetail u = new UserDetail();
            u.setUserId(user.getUserId());
            u.setStartTime(user.getStartTime());
            u.setEndTime(user.getEndTime());
            return u;
        }).collect(Collectors.toList());
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class AuthPrjTodoInfoDto {

        private String prjName;

        private LocalDateTime startTime;

        private LocalDateTime endTime;

        private String url;

        private String authPrjId;

        /**
         * 项目人员
         */
        private List<RvActivityParticipationMemberPO> users;

    }
}
