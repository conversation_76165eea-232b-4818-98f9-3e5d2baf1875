package com.yxt.talent.rv.application.activity.impt;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.enums.DeleteEnum;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.dto.PerfUserImportDTO;
import com.yxt.talent.rv.application.activity.dto.PerfUserImportResultVO;
import com.yxt.talent.rv.application.activity.dto.PeriodUserDTO;
import com.yxt.talent.rv.application.activity.expt.PerfUserImportErrorExportStrategy;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.EnableEnum;
import com.yxt.talent.rv.infrastructure.common.transfer.FileProcessedResult;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImportSupport;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileReader;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@Component
@RequiredArgsConstructor
public class PerfActivityImporter extends
    FileImporter<PerfUserImportDTO, PerfActivityImporter.PerfImportProcessedResult, PerfUserImportResultVO> {
    private static final String ERR_USERNAME_EMPTY = "apis.sptalentrv.perf.import.error.username";
    private static final String ERR_USERNAME_NOT_EXISTED =
        "apis.sptalentrv.perf.import.error.notexite";
    private static final String ERR_USERNAME_NOT_IN_PRJ =
        "apis.sptalentrv.perf.import.error.not_in_prj";
    private static final String ERR_USERNAME_DELETED =
        "apis.sptalentrv.perf.import.error.userdelete";
    private static final String ERR_USERNAME_DISABLED =
        "apis.sptalentrv.perf.import.error.userforbidden";
    private static final String ERR_PERIOD_NOT_EXISTED = "apis.sptalentrv.perf.import.error.period";
    private static final String ERR_LEVEL_NOT_ILLEGAL =
        "apis.sptalentrv.perf.import.error.perfgrade";


    private static final String ERR_PERF_SCORE_EMPTY = "请填写绩效总分";

    private static final String ERR_PERF_SCORE_ILLEGAL = "绩效总分非法数字";

    private static final String ERR_PERF_POINT_ILLEGAL = "绩效得分非法数字";

    private static final String ERR_PERF_SCORE_LEVEL_EMPTY = "请填写绩效等级或绩效得分";
    private static final String PERF_ERROR_EXPORT_FILE = "绩效导入";
    private static final String IMPORT_HEAD = "import:";
    private final PerfUserImportErrorExportStrategy perfImportErrorExportStrategy;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PerfPeriodMapper perfPeriodMapper;
    private final PerfGradeMapper perfGradeMapper;
    private final PerfMapper perfMapper;
    private final I18nComponent i18nComponent;
    private final XpdActivityParticipationMemberMapper xpdActivityParticipationMemberMapper;

    @Nullable
    public PerfUserImportResultVO toImport(
        FileImportCmd bean, MultipartFile file, UserCacheDetail userCache) {

        String orgId = userCache.getOrgId();
        String operator = userCache.getUserId();
        String lockKey = String.format(RedisKeys.LK_PERF_IMPT, IMPORT_HEAD, orgId, operator);
        String errorFileName = PERF_ERROR_EXPORT_FILE +
                               DateTimeUtil.dateToString(
                                   new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) +
                               FileConstants.FILE_SUFFIX_XLSX;

        Function<List<PerfUserImportDTO>, PerfImportProcessedResult> dataProcessor =
            importDataList -> dataProcess(importDataList, bean.getTargetId(), orgId, operator);

        FileImportSupport<PerfUserImportDTO, PerfImportProcessedResult, PerfUserImportResultVO>
            fileImportSupport =
            FileImportSupport.<PerfUserImportDTO, PerfImportProcessedResult, PerfUserImportResultVO>builder()
                .file(file)
                .fileId(bean.getFileId())
                .tranId(lockKey)
                .startRow(2)
                .orgId(orgId)
                .operator(operator)
                .importContentClazz(PerfUserImportDTO.class)
                .dataReader(new PerfActivityImporter.CliPerfFileReader(file, bean.getFileId()))
                .dataProcessor(dataProcessor)
                .outputStrategy(perfImportErrorExportStrategy)
                .errorFileName(errorFileName)
                .resultProcessor(this::generateImportResult)
                .build();
        return toImport(fileImportSupport);
    }

    private PerfUserImportResultVO generateImportResult(PerfImportProcessedResult processedResult) {
        int repeatCount = processedResult.getRepeatCount();
        int failedCount = processedResult.getFailedCount();
        int successCount = processedResult.getSuccessCount();
        int totalCount = processedResult.getTotalCount();
        List<PerfUserImportDTO> failedData = processedResult.getFailedData();
        Map<String, List<PerfUserImportDTO>> result = processedResult.getResult();
        String errorFilePath =
            Optional.ofNullable(processedResult.getErrorFilePath()).orElse(EMPTY);

        return PerfUserImportResultVO.childBuilder()
            .repeatCount(repeatCount)
            .failedCount(failedCount)
            .successCount(successCount)
            .totalCount(totalCount)
            .filePath(errorFilePath)
            .successData(result)
            .failData(failedData)
            .build();
    }

    @NotNull
    private PerfImportProcessedResult dataProcess(
        List<PerfUserImportDTO> importDataList, String xpdId, String orgId, String userId) {
        List<PerfUserImportDTO> tempImportDataList = new ArrayList<>();

        // 处理换行
        for (PerfUserImportDTO pe : importDataList) {
            pe.setUserName(dealSpecialSymbol(pe.getUserName()));
            if (StringUtils.isEmpty(pe.getUserName())) {
                continue;
            }
            tempImportDataList.add(pe);
        }
        importDataList = tempImportDataList;

        // 校验导入的账号信息
        // 检查账号列或所属关卡列是否都空
        List<PerfUserImportDTO> userList =
            StreamUtil.filterList(importDataList, b -> StringUtils.isNotBlank(b.getUserName()));
        if (CollectionUtils.isEmpty(userList)) {
            throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_NO_USERNAME);
        }

        // 获取导入用户名信息
        List<UdpLiteUserPO> importUsers = getImportUsers(importDataList, orgId);
        log.debug("LOG10090:{}", importUsers.size());
        Map<String, UdpLiteUserPO> importUserMaps =
            StreamUtil.list2map(importUsers, UdpLiteUserPO::getUsername);
        // 查询机构下所有的有效绩效周期
        List<PerfPeriodPO> periods = perfPeriodMapper.selectByOrgId(orgId);
        Map<String, String> periodNameMap =
            StreamUtil.list2map(periods, PerfPeriodPO::getPeriodName, PerfPeriodPO::getId);
        log.debug("LOG10110:{}", periodNameMap);
        List<String> periodNames =
            periods.stream().map(PerfPeriodPO::getPeriodName).collect(Collectors.toList());

        // 导入失败数
        int errorCount = importDataList.size();
        // 导入重复数
        int repeatCount = 0;
        // 处理结果map，key为周期名称
        Map<String, List<PeriodUserDTO>> result = new HashMap<>(8);
        // 查询是否开启了自定义绩效等级
        //OrgSettingPO orgSetting = orgSettingService.getOrgSetting(orgId, userId);
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgIdInState(orgId);
        // 机构是否开启了自定义机构绩效等级设置标记位，默认未开启
                /*boolean isOrgGrades = null != orgSetting && orgSetting.getGradeSet() == 1;
                log.debug("LOG10100:{}", isOrgGrades);*/
        List<String> gradesList = new ArrayList<>();
        Map<String, Integer> gradesMap = new HashMap<>(8);
        if (CollectionUtils.isNotEmpty(perfGrades)) {
            gradesList =
                perfGrades.stream().map(PerfGradePO::getGradeName).collect(Collectors.toList());
            gradesMap = StreamUtil.list2map(perfGrades, PerfGradePO::getGradeName,
                PerfGradePO::getGradeValue);
        }
        List<ActivityParticipationMember> members = xpdActivityParticipationMemberMapper.selectByXpdId(orgId, xpdId);
        List<String> memberIds = StreamUtil.mapList(members, ActivityParticipationMember::getUserId);

        // 遍历导入的数据处理
        for (PerfUserImportDTO perfImportDTO : importDataList) {
            // 账号检查
            checkUserName(perfImportDTO, importUserMaps, memberIds);
            if (StringUtils.isBlank(perfImportDTO.getErrorMsg())) {
                // 所属周期检查
                checkPeriodName(perfImportDTO, periodNames);
            }
            if (StringUtils.isBlank(perfImportDTO.getErrorMsg())) {
                // 绩效等级检查
                checkPeriodLevel(perfImportDTO, gradesList);
            }
            // 检查绩效分数,
            if (StringUtils.isBlank(perfImportDTO.getErrorMsg())) {
                checkPeriodScore(perfImportDTO);
            }

            if (StringUtils.isNotEmpty(perfImportDTO.getErrorMsg())) {
                // 数据异常，本条数据为异常数据不导入，跳出，执行下一条
                continue;
            }

            // 账号，周期，绩效等级正常之后放入参照map中
            for (String period : perfImportDTO.getPeriodNames()) {
                period = dealSpecialSymbol(period);

                PeriodUserDTO periodUserDTO = new PeriodUserDTO();
                periodUserDTO.setUserName(perfImportDTO.getUserName());
                periodUserDTO.setPeriodName(period);

                if (perfImportDTO.getLevelMap() != null && perfImportDTO.getLevelMap().containsKey(period)){
                    periodUserDTO.setPeriodLevel(dealSpecialSymbol(perfImportDTO.getLevelMap().get(period)));
                }else {
                    periodUserDTO.setPeriodLevel("");
                }

                if (perfImportDTO.getScoreMap() != null && perfImportDTO.getScoreMap().containsKey(period)){
                    periodUserDTO.setPeriodScore(dealSpecialSymbol(perfImportDTO.getScoreMap().get(period)));
                }else {
                    periodUserDTO.setPeriodScore("");
                }

                if (null != result.get(period)) {
                    List<PeriodUserDTO> existList = result.get(period);
                    // 检查已经存在的数据中是否已经存项相同的用户的数据
                    List<PeriodUserDTO> list = StreamUtil.filterList(
                        existList,
                        b -> StringUtils.equalsIgnoreCase(
                            b.getUserName(),
                            perfImportDTO.getUserName()));
                    if (CollectionUtils.isNotEmpty(list)) {
                        // 重复数据
                        repeatCount++;
                    } else {
                        // 不存在重复
                        existList.add(periodUserDTO);
                    }
                } else {
                    List<PeriodUserDTO> newList = new ArrayList<>();
                    newList.add(periodUserDTO);
                    result.put(period, newList);
                }
                errorCount--;
            }

        }
        // 存入收集的数据
        log.debug("LOG10120:{}", result.size());
        if (!result.isEmpty()) {
            for (Map.Entry<String, List<PeriodUserDTO>> entry : result.entrySet()) {
                String periodName = entry.getKey();
                String periodId = periodNameMap.get(periodName);
                // 批量插入
                batchSaveImports(orgId, userId, periodId, entry.getValue(), importUserMaps,
                    gradesMap);
            }
        }
        return PerfImportProcessedResult.builder() // NOSONAR
            .repeatCount(repeatCount)
//            .result(userResult)
            .totalData(importDataList)
            .failedData(StreamUtil.filterList(
                importDataList,
                b -> StringUtils.isNotBlank(b.getErrorMsg())))
            .successData(StreamUtil.filterList(
                importDataList,
                b -> StringUtils.isBlank(b.getErrorMsg())))
            .build();
    }

    private String dealSpecialSymbol(String param) {
        if (StringUtils.isBlank(param)) {
            return param;
        }
        // 保留换行符，不做替换处理
        return param;
    }

    private void checkPeriodScore(PerfUserImportDTO perfImportDTO) {
        if (!perfImportDTO.getScoreMap().isEmpty()) {
            for (Map.Entry<String, String> entry : perfImportDTO.getScoreMap().entrySet()) {
                String perfPoint = dealSpecialSymbol(entry.getValue());
                if (StringUtils.isNotBlank(perfPoint) && MathUtil.isNonPositiveOrZero(perfPoint)) {
                    perfImportDTO.setErrorMsg(ERR_PERF_POINT_ILLEGAL);
                    return;
                }
            }
        }

        for (String period : perfImportDTO.getPeriodNames()) {
            String perfPoint = dealSpecialSymbol(perfImportDTO.getScoreMap().get(period));
            String perflevel = dealSpecialSymbol(perfImportDTO.getLevelMap().get(period));
            // 绩效等级和绩效总分至少有一个有分数
            if (StringUtils.isBlank(perfPoint) && StringUtils.isBlank(perflevel)) {
                perfImportDTO.setErrorMsg(ERR_PERF_SCORE_LEVEL_EMPTY);
                return;
            }
        }
    }

    public void checkPeriodLevel(
        PerfUserImportDTO perfImportDTO, List<String> gradesList) {

        if (!perfImportDTO.getLevelMap().isEmpty()) {
            for (Map.Entry<String, String> entry : perfImportDTO.getLevelMap().entrySet()) {
                String level = dealSpecialSymbol(entry.getValue());
                // 如果绩效等级不为空且不在预设列表中
                if (StringUtils.isNotBlank(level) && !gradesList.contains(level)) {
                    String period = entry.getKey();
                    // 检查该周期是否有绩效得分
                    String perfPoint = StringUtils.EMPTY;
                    if (perfImportDTO.getScoreMap() != null && perfImportDTO.getScoreMap().containsKey(period)) {
                        perfPoint = dealSpecialSymbol(perfImportDTO.getScoreMap().get(period));
                    }
                    // 只有在没有绩效得分的情况下才报错
                    if (StringUtils.isBlank(perfPoint)) {
                        perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_LEVEL_NOT_ILLEGAL));
                        break;
                    }
                }
            }
        }
    }

    private void checkPeriodName(PerfUserImportDTO perfImportDTO, List<String> peroidNames) {
        for (String period : perfImportDTO.getPeriodNames()) {
            period = dealSpecialSymbol(period);
            // 不填
            if (StringUtils.isBlank(period)) {
                perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_PERIOD_NOT_EXISTED));
                return;
            }
            // 错误
            if (!peroidNames.contains(period)) {
                perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_PERIOD_NOT_EXISTED));
            }
        }
    }

    public void batchSaveImports(
        String orgId, String opUserId, String periodId, List<PeriodUserDTO> imports,
        Map<String, UdpLiteUserPO> importUserMaps,
        Map<String, Integer> gradesMap) {
        List<PerfPO> performancesAdd = new ArrayList<>();
        List<PerfPO> performancesUpdate = new ArrayList<>();

        PerfPeriodPO perfPeriodPO = perfPeriodMapper.selectByOrgIdAndId(orgId, periodId);


        for (PeriodUserDTO perfImportDTO : imports) {
            String userId = importUserMaps.get(perfImportDTO.getUserName()).getId();
            // 查询是否已经存在同绩效周期的数据
            PerfPO perf = perfMapper.selectByPeriodIdAndUserId(orgId, periodId, userId);
            // 不存在，新增
            if (null == perf) {
                perf = new PerfPO();
                EntityUtil.setAuditFields(perf, opUserId);
                perf.setId(ApiUtil.getUuid());
                perf.setOrgId(orgId);
                perf.setPeriodId(periodId);
                perf.setPerfPoint(transBigDecimal(perfImportDTO.getPeriodScore()));
                // 取绩效周期的值
                if (perfPeriodPO != null){
                    perf.setPerfScore(perfPeriodPO.getScoreTotal());
                }
                perf.setUserId(userId);
                Integer level = gradesMap.get(perfImportDTO.getPeriodLevel());
                if (level == null) {
                    perf.setPeriodLevel(-1);
                } else {
                    perf.setPeriodLevel(level);
                }
                performancesAdd.add(perf);
            } else {
                // 存在，更新
                Integer level = gradesMap.get(perfImportDTO.getPeriodLevel());
                if (level != null) {
                    perf.setPeriodLevel(level);
                } else {
                    perf.setPeriodLevel(-1);
                }


                perf.setPerfPoint(transBigDecimal(perfImportDTO.getPeriodScore()));
                // 取绩效周期的值
                if (perfPeriodPO != null){
                    perf.setPerfScore(perfPeriodPO.getScoreTotal());
                }
                EntityUtil.setUpdate(perf, opUserId);
                performancesUpdate.add(perf);
            }
        }
        if (CollectionUtils.isNotEmpty(performancesAdd)) {
            perfMapper.insertOrUpdateBatch(performancesAdd);
        }
        if (CollectionUtils.isNotEmpty(performancesUpdate)) {
            perfMapper.insertOrUpdateBatch(performancesUpdate);
        }
    }

    @Nullable
    private BigDecimal transBigDecimal(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        return new BigDecimal(param).setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * 用户账户check
     */
    private void checkUserName(
        PerfUserImportDTO perfImportDTO, Map<String, UdpLiteUserPO> importUserMaps, List<String> memberIds) {
        // 不填
        if (StringUtils.isBlank(perfImportDTO.getUserName())) {
            perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_EMPTY));
            return;
        }
        // 用户信息
        UdpLiteUserPO user = importUserMaps.get(perfImportDTO.getUserName());
        // 不存在
        if (user == null || StringUtils.isBlank(user.getId())) {
            perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_NOT_EXISTED));
            return;
        }
        // 删除
        if (user.getDeleted().intValue() == DeleteEnum.DELETED.getCode()) {
            perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_DELETED));
            return;
        }
        // 禁用
        if (user.getStatus().intValue() == EnableEnum.DISABLED.getCode()) {
            perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_DISABLED));
        }
        // 不存在于当前盘点项目人员中
        if (!memberIds.contains(user.getId())) {
            perfImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_NOT_IN_PRJ));
        }
    }

    private List<UdpLiteUserPO> getImportUsers(
        List<PerfUserImportDTO> perfImportDtos, String orgId) {
        Set<String> importUsernames = new HashSet<>(perfImportDtos.size());
        perfImportDtos.forEach(
            performance4Import -> importUsernames.add(performance4Import.getUserName()));
        return udpLiteUserMapper.selectActiveUsersByUserNames(orgId, importUsernames);
    }

    @Setter
    @Getter
    @ToString
    @SuperBuilder
    static class PerfImportProcessedResult extends FileProcessedResult<PerfUserImportDTO> {
        public final int repeatCount;
        public final Map<String, List<PerfUserImportDTO>> result;
    }

    @RequiredArgsConstructor
    private class CliPerfFileReader extends FileReader<PerfUserImportDTO> {

        private final MultipartFile file;

        private final String fileId;

        @Override
        @jakarta.annotation.Nonnull
        public List<PerfUserImportDTO> read() {
            return doReadExcel();
        }

        /**
         * 由于导入文件中有部分表头是动态生成的, 所以这里没办法使用静态导入的方式, 而是采用动态解析的方式来处理导入的数据
         */
        @jakarta.annotation.Nonnull
        private List<PerfUserImportDTO> doReadExcel() {
            ExcelReader excelReader = null;
            List<PerfUserImportDTO> importDataList;
            try (
                InputStream inputStream = this.getInputStream(file, fileId)
            ) {
                excelReader = EasyExcelFactory.read(inputStream).build();
                ReadSheet sheet = excelReader.excelExecutor().sheetList().get(0);

                // 读取sheet
                EasyExcelListener listener = new EasyExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(1);
                excelReader.read(sheet);

                // 提取Excel数据
                importDataList = generatePerfUser4ExcelImportList(listener);
            } catch (IOException e) {
                throw new ApiException(e.getMessage());
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
            }
            return importDataList;
        }


        @jakarta.annotation.Nonnull
        private List<PerfUserImportDTO> generatePerfUser4ExcelImportList(
            EasyExcelListener listener) {

            List<Map<Integer, String>> list = listener.getData();
            List<PerfUserImportDTO> muList = new ArrayList<>(list.size());

            // 表头
            Map<Integer, String> headMap = list.get(0);
            int allcellSize = headMap.size();
            if (headMap.isEmpty() || headMap.size() < 3) {
                throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR);
            }
            List<String> headPeriodNames = new ArrayList<>();
            for (int i = 2; i < list.size(); i++) {
                headPeriodNames.add(headMap.get(i));
            }

            if (CollectionUtils.isEmpty(headPeriodNames)) {
                throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR);
            }

            try {
                // 内容
                for (int j = 2; j < list.size(); j++) {
                    Map<Integer, String> map = list.get(j);
                    int i = 0;
                    PerfUserImportDTO temp = new PerfUserImportDTO();
                    temp.setUserName(map.get(i++));
                    temp.setFullName(map.get(i++));
                    List<String> periodNames = new ArrayList<>();
                    for (; i < allcellSize; ) {
                        if (i % 2 == 0) {
                            String periorName = headMap.get(i);
                            periodNames.add(periorName);
                            temp.getLevelMap().put(periorName, map.get(i++));
                        }
                        if (i % 2 == 1) {
                            String periorName = headMap.get(i - 1);
                            temp.getScoreMap().put(periorName, map.get(i++));
                        }
                    }
                    temp.setPeriodNames(periodNames);
                    muList.add(temp);
                }
            } catch (Exception e) {
                throw new ApiException(ExceptionKeys.ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR);
            }
            return muList;
        }
    }
}
