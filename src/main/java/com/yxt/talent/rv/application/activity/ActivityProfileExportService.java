package com.yxt.talent.rv.application.activity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.rv.application.activity.dto.*;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.MQConstant;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DownInfoUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.ubiz.export.bean.ExportExcelParam;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.bean.ExportParam;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import com.yxt.ubiz.export.core.AbstractExportWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ActivityProfileExportService extends AbstractExportWrapper {

    private final AuthService authService;
    private final ActivityProfileTslManager activityProfileTslManager;
    private final ActivityProfileMapper activityProfileMapper;
    private final RvAomActivityService aomActivityService;
    private final UdpLiteUserMapper udpLiteUserMapper;


    @Override
    public Map<String, String> getExportHeader(Object customParam) {
        return (Map<String, String>) customParam;
    }

    @Override
    public void loadData(Object queryParams, BiConsumer<List<?>, ExportParam> consumerList) {
        ActProfileUserExportParam search = JSON.parseObject(JSON.toJSONString(queryParams),
                ActProfileUserExportParam.class);
        String orgId = search.getOrgId();
        String actProfileId = search.getActProfileId();
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(actProfileId)) {
            consumerList.accept(new ArrayList<>(), new ExportExcelParam());
            return;
        }
        ActivityProfilePO activityProfilePO = activityProfileMapper.findByIdAndOrgId(actProfileId, orgId);
        if (Objects.isNull(activityProfilePO)) {
            consumerList.accept(new ArrayList<>(), new ExportExcelParam());
            return;
        }
        boolean flag = true;
        int current = 1;
        int size = 400;
        //        List<ActProfileUserListExportVO> resultList = new ArrayList<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        while (flag) {
            ActMemberUserCriteria searchParam = new ActMemberUserCriteria();
            searchParam.setKeyword(search.getSearchKey());
            searchParam.setDeptIds(search.getDeptIds());
            searchParam.setActvId(activityProfilePO.getAomActvId());
            searchParam.setPositionIds(search.getPositionIds());
            searchParam.setStatus(search.getUserStatus());
            Page<ActMemberUser> page = new Page<>(current, size);
            PagingList<ActMemberUser> pageResult = aomActivityService.getActivityUserList(page, orgId, searchParam);
            if (Objects.nonNull(pageResult) && CollectionUtils.isNotEmpty(pageResult.getDatas())) {
                List<String> userIds = pageResult.getDatas().stream().map(ActMemberUser::getUserId).distinct()
                        .collect(Collectors.toList());
                List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(orgId, userIds);
                Map<String, UdpLiteUserPO> userMap = users.stream().collect(Collectors.toMap(UdpLiteUserPO::getId, Function.identity(), (u, v) -> u));
                List<ProfilePageUserVO> profilePageUserVOList = activityProfileTslManager.getProfileUserInfo(orgId,
                        actProfileId, userIds);
                Map<String, ProfilePageUserVO> profilePageUserVOMap = StreamUtil.list2map(profilePageUserVOList,
                        ProfilePageUserVO::getUserId);
                userIds.forEach(userId -> {
                    UdpLiteUserPO userInfo = userMap.get(userId);
                    if (userInfo == null) {
                        return;
                    }
                    Map<String, String> exportData = new HashMap<>();
                    exportData.put("fullname", userInfo.getFullname());
                    exportData.put("username", userInfo.getUsername());
                    exportData.put("userStatus",
                            Integer.valueOf(1).equals(userInfo.getStatus()) ? "已启用" : "禁用");
                    exportData.put("deptName", userInfo.getDeptName());
                    exportData.put("positionName", userInfo.getPositionName());
                    //                    ActProfileUserListExportVO exportVO = new ActProfileUserListExportVO();
                    //                    exportVO.setFullname(userInfo.getFullname());
                    //                    exportVO.setUsername(userInfo.getUsername());
                    //                    exportVO.setUserStatus(
                    //                            Integer.valueOf(1).equals(userInfo.getUdpUser().getStatus()) ? "已启用" : "禁用");
                    //                    exportVO.setDeptName(userInfo.getUdpUser().getDeptName());
                    //                    exportVO.setPositionName(userInfo.getUdpUser().getPositionName());
                    ProfilePageUserVO profilePageUserVO = profilePageUserVOMap.get(userId);
                    String finsihStatusStr = "未完成";
                    if (Objects.nonNull(profilePageUserVO)) {
                        if (profilePageUserVO.getFinishedStatus() == 0) {
                            finsihStatusStr = "未完成";
                        }
                        if (profilePageUserVO.getFinishedStatus() == 1) {
                            finsihStatusStr = "进行中";
                        }
                        if (profilePageUserVO.getFinishedStatus() == 2) {
                            finsihStatusStr = "已完成";
                        }
                        List<ProfileUserIndicatorFinishVO> indicatorFinishResult = profilePageUserVO.getUserIndicatorFinishVOList();
                        if (CollectionUtils.isNotEmpty(indicatorFinishResult)) {
                            indicatorFinishResult.forEach(indicatorFinishVO -> {
                                if (StringUtils.isBlank(indicatorFinishVO.getIndicatorId())) {
                                    return;
                                }
                                //是否达标 -1-未完成，0-不达标, 1-达标,
                                int qualified = indicatorFinishVO.getQualified();
                                String qualifiedStr = "--";
                                if (qualified == 0) {
                                    qualifiedStr = "不达标";
                                }
                                if (qualified == -1) {
                                    qualifiedStr = "未完成";
                                }
                                if (qualified == 1) {
                                    qualifiedStr = "达标";
                                }
                                exportData.put(indicatorFinishVO.getIndicatorId(), qualifiedStr);
                            });
                        }
                    }
                    exportData.put("finishStatus", finsihStatusStr);
                    resultList.add(exportData);

                });
                consumerList.accept(resultList, new ExportExcelParam());
            }
            if (Objects.nonNull(pageResult) && CollectionUtils.isNotEmpty(pageResult.getDatas())
                    && pageResult.getDatas().size() >= size) {
                current++;
                continue;
            }
            flag = false;
        }
    }

    public Long exportFile(ActProfileUserExportParam bean, String name, String userId, String fullName,
            Map<String, String> customParam) {
        ExportFileInfo exportFileInfo = new ExportFileInfo();
        //设置业务的查询参数
        exportFileInfo.setQueryParams(bean);
        exportFileInfo.setCustomParam(customParam);
        //设置下载信息
        exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(AppConstants.FUNCTION_NAME));
        exportFileInfo.setLocale(authService.getLocale());
        exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
        //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
        exportFileInfo.setName(name);
        exportFileInfo.setFileName(name + System.nanoTime());
        exportFileInfo.setOrgId(bean.getOrgId());
        exportFileInfo.setUserId(userId);
        exportFileInfo.setFullname(fullName);
        exportFileInfo.setExportTopic(MQConstant.TOPIC_SPTALENTRV_EXPORT_FILE);
        exportFileInfo.setUsemq(false);
        exportFileInfo.setDownloadI18n(true);
        return export(exportFileInfo);
    }
}
