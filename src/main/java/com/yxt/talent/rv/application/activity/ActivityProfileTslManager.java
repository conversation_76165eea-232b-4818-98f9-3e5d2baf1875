package com.yxt.talent.rv.application.activity;

import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.activity.dto.ProfileActivityProfileIndicatorDTO;
import com.yxt.talent.rv.application.activity.dto.ProfilePageUserVO;
import com.yxt.talent.rv.application.activity.dto.ProfileUserIndicatorFinishVO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileIndicatorRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileResultDetailRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileResultRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ActivityProfile 事务处理业务或者通用业务
 *
 * <AUTHOR>
 * @since 2024/12/11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ActivityProfileTslManager {
    private final ActivityProfileResultRepo activityProfileResultRepo;
    private final ActivityProfileResultDetailRepo activityProfileResultDetailRepo;
    private final ActivityProfileRepo activityProfileRepo;
    private final ActivityProfileIndicatorRepo activityProfileIndicatorRepo;
    private final UdpLiteUserMapper udpLiteUserMapper;

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void delInsertNewData(String orgId, List<ActivityProfileResultPO> profileResultPOList,
            List<ActivityProfileResultDetailPO> profileResultDetailPOList, List<String> oldUserResultIds,
            List<ActivityProfileResultDetailPO> oldProfileResultDetailPOList) {
        //删除旧的数据
        activityProfileResultRepo.deleteByIds(orgId, oldUserResultIds);
        activityProfileResultDetailRepo.deleteByIds(orgId,
                oldProfileResultDetailPOList.stream().map(ActivityProfileResultDetailPO::getId)
                        .collect(Collectors.toList()));
        //批量保存新计算的匹配结果
        activityProfileResultRepo.batchInsert(profileResultPOList);
        activityProfileResultDetailRepo.batchInsert(profileResultDetailPOList);
    }


    public List<ProfilePageUserVO> getProfileUserInfo(String orgId, String actProfileId, List<String> userIds) {
        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        if (Objects.isNull(profilePO)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<ProfilePageUserVO> voList = new ArrayList<>();
        //查询rv_activity_profile_result 用户人才档案匹配活动结果表
        List<ActivityProfileResultPO> activityProfileResults = activityProfileResultRepo.findByActProfileIdAndUserIds(
                orgId, actProfileId, userIds);
        if (CollectionUtils.isEmpty(activityProfileResults)) {
            return new ArrayList<>();
        }
        List<String> uerIds = activityProfileResults.stream().map(ActivityProfileResultPO::getUserId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(orgId, uerIds);
        Map<String, UdpLiteUserPO> userMap = users.stream().collect(Collectors.toMap(UdpLiteUserPO::getId, Function.identity(), (u, v) -> u));
        //查询活动指标数据
        List<ActivityProfileIndicatorPO> activityProfileIndicatorList = activityProfileIndicatorRepo.findByActProfileId(
                orgId, actProfileId);
        List<ProfileActivityProfileIndicatorDTO> indicatorDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityProfileIndicatorList)) {
            activityProfileIndicatorList.forEach(indicatorItem -> {
                ProfileActivityProfileIndicatorDTO indicatorDTO = new ProfileActivityProfileIndicatorDTO();
                indicatorDTO.setIndicatorId(indicatorItem.getSdIndicatorId());
                //                indicatorDTO.setIndicatorName();
                indicatorDTOList.add(indicatorDTO);
            });
        }
        Map<String, List<ActivityProfileResultPO>> activityProfileResultmap = activityProfileResults.stream()
                .collect(Collectors.groupingBy(ActivityProfileResultPO::getUserId));
        activityProfileResultmap.forEach((uid, indicatorResList) -> {
            ProfilePageUserVO vo = new ProfilePageUserVO();
            if (!userMap.isEmpty() && userMap.containsKey(uid)) {
                //设置用户基本信息
                setUserBaseInfo(userMap, uid, vo);
            }
            if (CollectionUtils.isNotEmpty(indicatorDTOList)) {
                if (CollectionUtils.isNotEmpty(indicatorResList)
                        && indicatorDTOList.size() == indicatorResList.size()) {
                    vo.setFinishedStatus(2);
                }
                if (CollectionUtils.isNotEmpty(indicatorResList)
                        && indicatorDTOList.size() != indicatorResList.size()) {
                    vo.setFinishedStatus(1);
                }
                if (CollectionUtils.isEmpty(indicatorResList)) {
                    vo.setFinishedStatus(0);
                }
                List<ProfileUserIndicatorFinishVO> userIndicatorFinishVOList = new ArrayList<>();
                Map<String, Integer> indicatorResMap = indicatorResList.stream().collect(
                        Collectors.toMap(ActivityProfileResultPO::getSdIndicatorId,
                                ActivityProfileResultPO::getQualified, (u, v) -> u));
                indicatorDTOList.forEach(indicatorItem -> {
                    ProfileUserIndicatorFinishVO indicatorFinishVO = new ProfileUserIndicatorFinishVO();
                    indicatorFinishVO.setIndicatorId(indicatorItem.getIndicatorId());
                    indicatorFinishVO.setIndicatorName(indicatorItem.getIndicatorName());
                    indicatorFinishVO.setQualified(indicatorResMap.getOrDefault(indicatorItem.getIndicatorId(), -1));
                    userIndicatorFinishVOList.add(indicatorFinishVO);
                });
                vo.setUserIndicatorFinishVOList(userIndicatorFinishVOList);
            }
            voList.add(vo);
        });
        return voList;
    }

    private void setUserBaseInfo(Map<String, UdpLiteUserPO> userMap, String uid, ProfilePageUserVO vo) {
        UdpLiteUserPO userInfo = userMap.get(uid);
        vo.setUserId(userInfo.getId());
        vo.setUsername(userInfo.getUsername());
        vo.setFullname(userInfo.getFullname());
        vo.setDeptId(userInfo.getDeptId());
        vo.setDeptName(userInfo.getDeptName());
        vo.setPositionId(userInfo.getPositionId());
        vo.setPositionName(userInfo.getPositionName());
        vo.setUserStatus(userInfo.getStatus());
    }
}
