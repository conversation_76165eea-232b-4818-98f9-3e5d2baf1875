package com.yxt.talent.rv.application.authprj;

import com.yxt.aom.base.mapper.part.AomActivityParticipationMemberScoreMapper;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.authprj.dto.UserRefActiveResultDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityArrangeItemMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityObjectiveResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvAssessmentActivityResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvAssessmentActivityResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO;
import com.yxt.talent.rv.infrastructure.service.remote.MqAclSender;
import com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj.AuthPrjResultMsg;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.AUTHPRJ_NOT_EXIST;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_AUTH_PRJ_RESULT;

/**
 * 认证计算相关服务
 */
@Slf4j
@Service
@AllArgsConstructor
public class AuthPrjCalcAppService {

    public static final String OPTION_CODE_PROJECT_SCORE = "PROJECT_SCORE";
    public static final String OPTION_CODE_PREFIX_EXAM = "EXAM";
    public static final String OPTION_CODE_PREFIX_EVAL = "EVAL";
    public static final String OPTION_CODE_PREFIX_IDENTIFY = "IDENTIFY";
    private final AuthprjResultUserMapper authprjResultUserMapper;
    private final AuthprjResultUserIndicatorMapper authprjResultUserIndicatorMapper;
    private final RvAssessmentActivityResultMapper rvAssessmentActivityResultMapper;
    private final AuthprjMapper authprjMapper;
    private final AuthPrjIndicatorResultService authPrjIndicatorResultService;
    private final AuthPrjLevelResultService authPrjLevelResultService;
    private final AuthPrjCertIssueService authPrjCertIssueService;
    private final MqAclSender mqAclSender;
    private final AuthPrjTodoService authPrjTodoService;
    private final AuthPrjCalcTaskService authPrjCalcTaskService;
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;
    private final AomActivityParticipationMemberScoreMapper aomActivityParticipationMemberScoreMapper;
    private final RvActivityArrangeItemMapper rvActivityArrangeItemMapper;
    private final RvActivityObjectiveResultMapper rvActivityObjectiveResultMapper;

    /**
     * 查询认证项目用户活动得分
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @param actvRefId 认证活动的refId
     * @return 活动得分，如果未找到返回null
     */
    @Nullable
    public BigDecimal queryActivityScore(String orgId, String authprjId, String userId, String actvRefId) {
        try {
            if (OPTION_CODE_PROJECT_SCORE.equals(actvRefId)) {
                // 查询项目总得分
                return authPrjIndicatorResultService.calculateUserTotalScore(orgId, authprjId, userId);
            }

            // 解析活动类型和ID
            String[] parts = actvRefId.split("_", 2);
            if (parts.length != 2) {
                log.warn("LOG10036:无效的活动ID格式: {}", actvRefId);
                return null;
            }

            String realActvRefId = parts[1];
            return queryActvScore(orgId, authprjId, realActvRefId, userId);
        } catch (Exception e) {
            log.error(
                "LOG10056:查询活动得分失败, orgId={}, authprjId={}, userId={}, actvRefId={}",
                orgId, authprjId, userId, actvRefId, e);
            return null;
        }
    }

    /**
     * 查询认证项目用户活动结果
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @param actvRefId 活动ID（格式：IDENTIFY_xxx）
     * @return 活动结果，1-通过，0-不通过，null-未找到
     */
    @Nullable
    public Integer queryActvPassed(String orgId, String authprjId, String userId, String actvRefId) {
        try {
            // 解析活动类型和ID
            String[] parts = actvRefId.split("_", 2);
            if (parts.length != 2) {
                log.warn("LOG10026:无效的活动ID格式: {}", actvRefId);
                return null;
            }
            String realActivityId = parts[1];
            return doQueryActvPassed(orgId, authprjId, realActivityId, userId);
        } catch (Exception e) {
            log.error(
                "LOG10116:查询活动结果失败, orgId={}, authprjId={}, userId={}, actvRefId={}",
                orgId, authprjId, userId, actvRefId, e);
            return null;
        }
    }

    /**
     * 查询活动得分
     */
    @Nullable
    private BigDecimal queryActvScore(String orgId, String authprjId, String actvRefId, String userId) {
        try {
            // 查询活动的指标结果
            AuthprjPO authprjPO = authprjMapper.selectById(authprjId);
            Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);

            List<UserRefActiveResultDTO> results =
                rvActivityObjectiveResultMapper.selectByActvIdAndRefIdAndUserId(orgId, authprjPO.getAomPrjId(), actvRefId, userId);

            if (results.isEmpty()) {
                return null;
            }

            // 计算总得分
            return results.stream()
                .map(UserRefActiveResultDTO::getObjectiveScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error(
                "LOG10096:查询考试活动得分失败, orgId={}, actvRefId={}, userId={}", orgId, actvRefId, userId, e);
            return null;
        }
    }

    /**
     * 查询活动结果
     */
    @Nullable
    private Integer doQueryActvPassed(String orgId, String authprjId, String actvRefId, String userId) {
        try {
            // 查询鉴定活动的绩效结果
            AuthprjPO authprjPO = authprjMapper.selectById(authprjId);
            Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);
            RvAssessmentActivityResultPO result =
                rvAssessmentActivityResultMapper.selectByActvRefIdAndUserId(orgId, authprjPO.getAomPrjId(), actvRefId, userId);

            return Optional.ofNullable(result).map(RvAssessmentActivityResultPO::getPassed).orElse(null);
        } catch (Exception e) {
            log.error("查询鉴定活动结果失败, orgId={}, actvRefId={}, userId={}", orgId, actvRefId, userId, e);
            return null;
        }
    }

    /**
     * 触发项目计算
     * 新版本：移除isForce参数，每次触发都会生成新的traceId，通过版本隔离避免冲突
     * 
     * @param orgId 机构ID
     * @param authPrjId 认证项目ID
     * @param isSync 是否同步计算
     */
    public void triggerProjectCalc(String orgId, String authPrjId, boolean isSync) {
        String traceId = ApiUtil.getUuid();
        log.info("LOG41036:开始触发项目计算所有用户, orgId={}, authPrjId={}, traceId={}", orgId, authPrjId, traceId);

        try {
            // 查询所有用户
            AuthprjPO authprjPO = authprjMapper.selectById(authPrjId);
            Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);
            List<String> userIds = rvActivityParticipationMemberMapper.findAllUserIdByActId(orgId, authprjPO.getAomPrjId());

            // 标记计算开始，异步模式下传递用户数量用于计数
            int asyncUserCount = isSync ? 0 : userIds.size();
            authPrjCalcTaskService.startCalculation(orgId, authPrjId, traceId, asyncUserCount);

            for (String userId : userIds) {
                if (isSync) {
                    // 同步处理
                    processUserAuthResult(orgId, userId, authPrjId, traceId);
                } else {
                    // 构建消息体，发送MQ
                    AuthPrjResultMsg msg = AuthPrjResultMsg.builder()
                        .orgId(orgId)
                        .userId(userId)
                        .authPrjId(authPrjId)
                        .traceId(traceId)
                        .build();

                    mqAclSender.send(TOPIC_AUTH_PRJ_RESULT, BeanHelper.bean2Json(msg, ALWAYS));
                }
            }

            // 同步计算立即标记完成
            if (isSync) {
                authPrjCalcTaskService.completeCalculation(orgId, authPrjId, traceId, true, "同步计算完成");
            }
            // 异步计算的完成状态需要在消息处理完成后单独设置

        } catch (Exception e) {
            log.error("LOG41066:触发项目计算失败: orgId={}, authPrjId={}, traceId={}", orgId, authPrjId, traceId, e);
            authPrjCalcTaskService.completeCalculation(orgId, authPrjId, traceId, false, "计算失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 处理用户认证结果
     *
     * @param orgId 机构ID
     * @param userId 用户ID
     * @param authPrjId 认证项目ID
     * @param traceId 追踪ID
     */
    public void processUserAuthResult(String orgId, String userId, String authPrjId, String traceId) {
        try {
            log.info("LOG10020:开始处理用户认证结果, orgId={}, userId={}, authPrjId={}, traceId={}",
                orgId, userId, authPrjId, traceId);

            // 查找对应的认证项目
            AuthprjPO authprjPO = authprjMapper.selectById(authPrjId);
            if (authprjPO == null) {
                log.warn("LOG10021:未找到对应的认证项目, orgId={}, authPrjId={}", orgId, authPrjId);
                return;
            }

            // 查询用户认证结果
            AuthprjResultUserPO oldResult = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
            int oldTaskCompleted = oldResult == null ? 0 : oldResult.getTaskCompleted();
            log.info("LOG42046:查询用户认证结果, authPrjId={}, userId={}, oldTaskCompleted={}", authPrjId, userId, oldTaskCompleted);

            // 清理历史数据
            authPrjIndicatorResultService.deleteUserIndicatorResults(orgId, authprjPO.getId(), userId);
            authPrjLevelResultService.deleteUserLevelResult(orgId, authprjPO.getId(), userId);

            // 处理指标结果计算与存储
            authPrjIndicatorResultService.processUserIndicatorResults(orgId, authprjPO.getId(), userId, authprjPO.getAomPrjId());

            // 计算分层结果
            authPrjLevelResultService.calculateUserLevelResult(orgId, authprjPO.getId(), userId);

            // 自动颁发证书
            authPrjCertIssueService.checkAndIssueCerts(orgId, authprjPO.getId(), userId);

            // 处理用户待办
            doHandleUserTodo(orgId, userId, authPrjId, oldTaskCompleted);

            log.info("LOG42036:处理用户认证结果完成, authPrjId={}, userId={}", authprjPO.getId(), userId);

        } catch (Exception e) {
            log.error(
                "LOG42376:处理用户认证结果失败, orgId={}, userId={}, authPrjId={}, error={}",
                orgId, userId, authPrjId, e.getMessage(), e);
            throw e;
        }
    }

    private void doHandleUserTodo(String orgId, String userId, String authPrjId, int oldTaskCompleted) {
        // 重新查询用户认证结果，获取最新的完成状态
        AuthprjResultUserPO newResult = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        int newTaskCompleted = newResult == null ? 0 : newResult.getTaskCompleted();

        // 任务从“未完成”变为“已完成”
        if (oldTaskCompleted == 0 && newTaskCompleted == 1) {
            log.info("LOG40006:用户认证项目任务完成，触发完成待办. orgId={}, userId={}, authPrjId={}", orgId, userId,
                authPrjId);
            authPrjTodoService.finishUserTodo(orgId, userId, authPrjId, Collections.singletonList(userId));
        }

        // 任务从“已完成”变为“未完成”
        if (oldTaskCompleted == 1 && newTaskCompleted == 0) {
            log.info("LOG40016:用户认证项目任务状态由完成变为未完成，触发创建待办. orgId={}, userId={}, authPrjId={}",
                orgId, userId, authPrjId);
            authPrjTodoService.addUserTodo(orgId, userId, authPrjId, Collections.singletonList(userId));
        }
    }

    public void dealWhenAomMemberStatisticChange(String orgId, String userId, String actvId) {
        String traceId = ApiUtil.getUuid();
        log.info("LOG42276:开始处理用户认证结果, orgId={}, userId={}, authPrjId={}, traceId={}", orgId, userId, actvId, traceId);
        AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, actvId);
        if (authprjPO == null) {
            log.debug("LOG42266:未找到对应的认证项目, orgId={}, actvId={}", orgId, actvId);
            return;
        }
        this.processUserAuthResult(orgId, userId, authprjPO.getId(), traceId);
    }
}
