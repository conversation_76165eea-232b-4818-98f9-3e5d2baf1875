package com.yxt.talent.rv.application.authprj.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.custom.MultiActivityCompo;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.datamodel.activityresult.BaseActivityResult;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.service.remote.MqAclSender;
import com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj.AuthPrjResultMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_AUTH_PRJ_RESULT;

/**
 * 用户结果回调，aom处理入库之后，提供的插槽，供业务方触发自己的逻辑
 */
@Slf4j
@RequiredArgsConstructor
@Component("multiActivityCompo4ProjAuthpj")
public class AomUserResultCallbackCompo implements MultiActivityCompo {

    private final MqAclSender mqAclSender;
    private final AuthprjMapper authprjMapper;

    @Override
    public <T extends BaseActivityResult> void resultConsumedCallBack(
        Actor actor, TargetObject targetObject, ActivityArrangeItem item, String traceId, T baseActivityResult) {
        log.info("LOG42256:{}", BeanHelper.bean2Json(targetObject, JsonInclude.Include.ALWAYS));

        String userId = actor.getUserId();
        // 注意这里是项目id，一个项目下有若干活动id， 我们这个插槽中关注的是用户整个项目的结果
        String actvId = targetObject.getSourceId();
        String orgId = item.getOrgId();

        log.info(
            "LOG20016:开始发送用户认证结果消息, userId={}, orgId={}, actvId={}, traceId={}",
            userId, orgId, actvId, traceId);

        try {

            AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, actvId);
            if (authprjPO == null) {
                log.debug("LOG42386:未找到对应的认证项目, orgId={}, actvId={}", orgId, actvId);
                return;
            }

            // 构建消息体，发送MQ
            AuthPrjResultMsg msg = AuthPrjResultMsg.builder()
                .orgId(orgId)
                .userId(userId)
                .authPrjId(authprjPO.getId())
                .traceId(traceId)
                .build();

            mqAclSender.send(TOPIC_AUTH_PRJ_RESULT, BeanHelper.bean2Json(msg, ALWAYS));

            log.info("LOG20006:用户认证结果消息发送成功, userId={}, orgId={}, actvId={}", userId, orgId, actvId);

        } catch (Exception e) {
            log.error(
                "LOG20026:发送用户认证结果消息失败, userId={}, orgId={}, actvId={}, error={}",
                userId, orgId, actvId, e.getMessage(), e);
            // 这里不重新抛出异常，避免影响AOM的主流程
        }
    }
}
