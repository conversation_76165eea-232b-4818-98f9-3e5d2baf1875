package com.yxt.talent.rv.application.authprj;

import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.common.bean.RuleOptionBean;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.controller.manage.authprj.cmd.AuthprjRuleLevelModifyCmd;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthprjRuleLevelVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityArrangeItemMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjRuleLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjRuleLevelPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 认证规则相关服务
 */
@Slf4j
@Service
@AllArgsConstructor
public class AuthPrjRuleAppService {

    private final AuthprjMapper authprjMapper;
    private final RvActivityArrangeItemMapper rvActivityArrangeItemMapper;
    private final AuthprjRuleLevelMapper authprjRuleLevelMapper;
    private final AuthPrjCalcAppService authPrjCalcAppService;

    /**
     * 获取认证项目下的考试活动列表
     *
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @return 考试活动选项列表
     */
    public List<RuleOptionBean> getExamActivities(String orgId, String authprjId) {
        return getActivitiesByType(orgId, authprjId, Collections.singletonList(UacdTypeEnum.ACTV_EXAM.getRegId()));
    }

    /**
     * 获取认证项目下的测评活动列表
     *
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @return 测评活动选项列表
     */
    public List<RuleOptionBean> getEvalActivities(String orgId, String authprjId) {
        return getActivitiesByType(orgId, authprjId, Collections.singletonList(UacdTypeEnum.ACTV_SPEVAL.getRegId()));
    }

    /**
     * 获取认证项目下的鉴定活动列表
     *
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @return 鉴定活动选项列表
     */
    public List<RuleOptionBean> getIdentifyActivities(String orgId, String authprjId) {
        // 鉴定活动可能对应多种类型，这里先用测评类型作为示例
        return getActivitiesByType(orgId, authprjId, Collections.singletonList(UacdTypeEnum.ACTV_JD.getRegId()));
    }

    /**
     * 根据活动类型获取认证项目下的活动列表
     *
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @param actvRegIds 活动注册IDs
     * @return 活动选项列表
     */
    public List<RuleOptionBean> getActivitiesByType(String orgId, String authprjId, List<String> actvRegIds) {
        List<RuleOptionBean> options = new ArrayList<>();

        try {
            // 根据认证项目ID获取认证项目信息
            AuthprjPO authprj = authprjMapper.selectById(authprjId);
            if (authprj == null || !orgId.equals(authprj.getOrgId())) {
                return options;
            }

            // 根据项目ID获取活动安排项
            List<ActivityArrangeItem> arrangeItems = rvActivityArrangeItemMapper.listArrangeItem(
                orgId, authprj.getAomPrjId(), null, 0); // itemType=0表示叶节点(任务)

            // 过滤指定类型的活动
            for (ActivityArrangeItem item : arrangeItems) {
                for (String actvRegId : actvRegIds) {
                    if (actvRegId.equals(item.getRefRegId())) {
                        RuleOptionBean option = new RuleOptionBean();
                        option.setId(item.getRefId());
                        option.setName(item.getRefName());
                        options.add(option);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取认证项目活动列表失败, orgId={}, authprjId={}, actvRegIds={}",
                orgId, authprjId, actvRegIds, e);
        }

        return options;
    }

    /**
     * 查询认证项目分层规则列表
     */
    public List<AuthprjRuleLevelVO> list(String orgId, String authprjId) {
        AuthprjPO authprj = authprjMapper.selectById(authprjId);
        Validate.isNotNull(authprj, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        List<AuthprjRuleLevelPO> poList = authprjRuleLevelMapper.selectByAuthprjId(orgId, authprjId);
        return poList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    /**
     * 分层规则
     */
    public void modify(String orgId, String operator, String authprjId, List<AuthprjRuleLevelModifyCmd> cmdList) {
        AuthprjPO authprj = authprjMapper.selectById(authprjId);
        Validate.isNotNull(authprj, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        Validate.isTrue(!(CollectionUtils.isEmpty(cmdList) || cmdList.size() < 2 || cmdList.size() > 20), ExceptionKeys.AUTHPRJ_RULE_LEVEL_INVALID);

        // 1. 获取数据库中现有的规则
        List<AuthprjRuleLevelPO> existingRules = authprjRuleLevelMapper.selectByAuthprjId(orgId, authprjId);
        Map<String, AuthprjRuleLevelPO> existingRulesMap = existingRules.stream()
            .collect(Collectors.toMap(AuthprjRuleLevelPO::getId, po -> po, (existing, replacement) -> existing));

        // 2. 准备待新增、待更新的列表
        List<AuthprjRuleLevelPO> upsertList = new ArrayList<>();

        // 3. 遍历传入的命令，区分新增和更新
        for (AuthprjRuleLevelModifyCmd cmd : cmdList) {
            if (StringUtils.hasText(cmd.getLevelId())) {
                // 更新
                AuthprjRuleLevelPO poToUpdate = existingRulesMap.get(cmd.getLevelId());
                if (poToUpdate != null) {
                    BeanUtils.copyProperties(cmd, poToUpdate, "levelId");
                    poToUpdate.setUpdateUserId(operator);
                    poToUpdate.setUpdateTime(LocalDateTime.now());
                    upsertList.add(poToUpdate);
                    existingRulesMap.remove(cmd.getLevelId());
                } else {
                    // ID存在但DB中没有，视为新增
                    upsertList.add(convertToPO(cmd, orgId, authprjId, operator));
                }
            } else {
                // 新增
                upsertList.add(convertToPO(cmd, orgId, authprjId, operator));
            }
        }

        // 4. 确定需要删除的记录
        List<String> idsToDelete = new ArrayList<>(existingRulesMap.keySet());

        boolean needCalc = false;

        // 5. 插入或更新
        if (!upsertList.isEmpty()) {
            // 设置序列号
            for (int i = 0; i < upsertList.size(); i++) {
                upsertList.get(i).setOrderIndex(i + 1);
            }
            authprjRuleLevelMapper.batchInsertOrUpdate(upsertList);
            needCalc = true;
        }

        if (!idsToDelete.isEmpty()) {
            authprjRuleLevelMapper.deleteBatchIds(idsToDelete);
            needCalc = true;
        }

        // 6. 重新计算用户的认证状态
        if (needCalc) {
            log.info("LOG42206:authPrjId={},重新计算用户的认证状态", authprjId);
            authPrjCalcAppService.triggerProjectCalc(orgId, authprjId, false);
        }
    }

    /**
     * PO转DTO
     */
    private AuthprjRuleLevelVO convertToDto(AuthprjRuleLevelPO po) {
        AuthprjRuleLevelVO vo = new AuthprjRuleLevelVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    private AuthprjRuleLevelPO convertToPO(
        AuthprjRuleLevelModifyCmd cmd, String orgId, String authprjId, String operator) {
        AuthprjRuleLevelPO po = new AuthprjRuleLevelPO();
        BeanUtils.copyProperties(cmd, po, "levelId");
        po.setOrgId(orgId);
        po.setAuthprjId(authprjId);
        EntityUtil.setAuditFields(po, operator);
        return po;
    }

    public void clear(String orgId, String authprjId, String operator) {
        AuthprjPO authprj = authprjMapper.selectById(authprjId);
        Validate.isNotNull(authprj, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        authprjRuleLevelMapper.deleteByAuthprjId(orgId, authprjId, operator);
    }

}
