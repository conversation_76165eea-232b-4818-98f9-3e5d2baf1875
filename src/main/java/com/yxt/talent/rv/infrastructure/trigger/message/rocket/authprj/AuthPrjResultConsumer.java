package com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj;

import com.yxt.talent.rv.application.authprj.AuthPrjCalcAppService;
import com.yxt.talent.rv.application.authprj.AuthPrjCalcTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_AUTH_PRJ_RESULT;

/**
 * 认证项目用户结果处理消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(topic = TOPIC_AUTH_PRJ_RESULT,
    consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_AUTH_PRJ_RESULT,
    consumeThreadNumber = 3, consumeTimeout = 30)
public class AuthPrjResultConsumer implements RocketMQListener<AuthPrjResultMsg> {

    private final AuthPrjCalcAppService authPrjCalcAppService;
    private final AuthPrjCalcTaskService authPrjCalcTaskService;

    @Override
    public void onMessage(AuthPrjResultMsg msg) {
        String orgId = msg.getOrgId();
        String userId = msg.getUserId();
        String authPrjId = msg.getAuthPrjId();
        String traceId = msg.getTraceId();
        boolean success = true;

        log.info(
            "LOG20036:开始处理用户认证结果消息, userId={}, orgId={}, authPrjId={}, traceId={}",
                userId, orgId, authPrjId, traceId);

        try {
            // 版本校验：检查消息中的traceId是否为当前活跃的计算ID
            String currentCalcId = authPrjCalcTaskService.getCurrentCalcId(orgId, authPrjId);
            if (currentCalcId == null || !currentCalcId.equals(traceId)) {
                log.info("LOG41096:消息版本已过期，跳过处理: userId={}, orgId={}, authPrjId={}, msgTraceId={}, currentCalcId={}",
                        userId, orgId, authPrjId, traceId, currentCalcId);
                return; // 直接返回，不处理过期消息
            }

            // 版本校验通过，正常处理业务逻辑
            authPrjCalcAppService.processUserAuthResult(orgId, userId, authPrjId, traceId);
            log.info("LOG20046:用户认证结果处理完成, userId={}, orgId={}, authPrjId={}, traceId={}", 
                    userId, orgId, authPrjId, traceId);

        } catch (Exception e) {
            log.error("LOG20056:处理用户认证结果消息失败, userId={}, orgId={}, authPrjId={}, traceId={}, error={}", 
                    userId, orgId, authPrjId, traceId, e.getMessage(), e);
            success = false;
            // 根据业务需求决定是否需要重试，这里选择不抛出异常，避免MQ无限重试
        } finally {
            // 【关键改动】无论成功失败，都需要递减计数器，以确保任务能够正确完成
            // 但只有版本校验通过的消息才能递减计数器
            String currentCalcId = authPrjCalcTaskService.getCurrentCalcId(orgId, authPrjId);
            if (currentCalcId != null && currentCalcId.equals(traceId)) {
                authPrjCalcTaskService.decreaseAndCompleteAsync(orgId, authPrjId, traceId, userId, success);
            }
        }
    }
}
