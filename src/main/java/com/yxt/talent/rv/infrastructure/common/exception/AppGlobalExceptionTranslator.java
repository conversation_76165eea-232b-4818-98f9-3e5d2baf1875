package com.yxt.talent.rv.infrastructure.common.exception;

import com.yxt.common.aop.JsonExceptionHandler;
import com.yxt.common.repo.ConfigRepository;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.MessageSourceService;
import com.yxt.spsdk.logsave.LogRecorder;
import com.yxt.usdk.framework.webimpl.service.WafCustomExceptionHandler;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;

@Slf4j
@Primary
@Component
@ConditionalOnProperty(name = "app.exception.translate.enabled", havingValue = "true", matchIfMissing = true)
public class AppGlobalExceptionTranslator extends JsonExceptionHandler {
    private static final String MSG_INVALID =
            "{\"code\":400,\"message\":\"Invalid error message!\"}";
    public static final String APIS_SPTALENTRV_ERROR = "apis.sptalentrv.sys.error";

    private final MessageSourceService msgSource;
    private final AuthService authService;
    private final ConfigRepository configRepository;

    public AppGlobalExceptionTranslator(
            MessageSourceService msgSource, AuthService authService,
            ConfigRepository configRepository, ObjectProvider<List<WafCustomExceptionHandler>> customExceptionHandlers) {
        super(msgSource, authService, configRepository, customExceptionHandlers);
        this.msgSource = msgSource;
        this.authService = authService;
        this.configRepository = configRepository;
    }

    /**
     * 集成父类的处理方法，处理所有其他未知异常
     *
     * @param request
     * @param ex      Exception
     * @return
     */
    @Override
    public ResponseEntity<Object> handleGeneralException(HttpServletRequest request, Exception ex) {
        //        return super.handleGeneralException(request, ex);
        // 获取原始类型异常
        Throwable originEx = ex.getCause();
        if (originEx instanceof FeignException) {
            return handleFeignException(request, (FeignException) originEx);
        }
        return handleException(request, ex);
    }

    /**
     * 处理所有其他未知异常，将其转换以为400异常
     *
     * @param request
     * @param ex
     * @return
     */
    public ResponseEntity<Object> handleException(HttpServletRequest request, Exception ex) {
        recordError(request, ex);
        String key = APIS_SPTALENTRV_ERROR;
        String msg = doGetMessage(key, authService.getLocale());
        return configRepository.isReturnErrorTrace() ?
                generateResponse(key, msg, new String[]{ExceptionUtils.getStackTrace(ex)}) :
                generateResponse(key, msg);
    }

    private String doGetMessage(String code, Locale locale) {
        String msg;
        try {
            msg = msgSource.getMessage(code, null, locale);
        } catch (Exception e) {
            msg = MSG_INVALID;
//            log.warn("LOG63080:{}", ExceptionUtil.getSimpleMessage(e));
            LogRecorder.error("", log, "LOG63080", e);
        }
        return msg;
    }

    private void recordError(HttpServletRequest request, Exception ex) {
        String requestUri = extractRequestUri(request);
        String method = request.getMethod();
        LogRecorder.error("", log, "LOG50020:method={}, requestUri={}", method, requestUri, ex);
//        log.error("LOG00020:method={}, requestUri={}", method, requestUri, ex);
    }

    private String extractRequestUri(HttpServletRequest request) {
        String requestUri;
        String queryString = request.getQueryString();
        if (queryString != null && !queryString.isEmpty()) {
            requestUri = String.format("%s?%s", request.getRequestURI(), queryString);
        } else {
            requestUri = request.getRequestURI();
        }

        return requestUri;
    }
}
