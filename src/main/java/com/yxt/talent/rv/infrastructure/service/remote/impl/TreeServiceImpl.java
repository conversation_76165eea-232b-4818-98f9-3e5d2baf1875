package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.talent.rv.infrastructure.service.remote.TreeService;
import com.yxt.ubiz.tree.extension.UTreeComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class TreeServiceImpl implements TreeService {
    private final UTreeComponent uTreeComponent;

    @Override
    public Map<String, String> getTreeNameMap(String orgId, String treeId, List<String> categoryIds) {
        Map<String, String> map =  uTreeComponent.getNodesName(orgId, treeId,categoryIds);
        if (!map.containsKey("1")){
            map.put("1", "默认分类");
        }
        return map;
    }
}
