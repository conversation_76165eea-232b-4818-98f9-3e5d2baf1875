package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

/**
 * P: publish-代表发布方
 * S：subscribe-代表订阅方
 * -----------------------
 * E：external-代表由外部服务提供的topic
 * I：inner-代表由我们自己服务提供的topic
 * -----------------------
 * O: 代表有序队列
 * C: 代表集群队列，即无序队列
 * B: 广播模式，也是无序的
 * <p>
 * 三者组合可以标识一个Topic的来源和订阅者
 */
@UtilityClass
public final class MQConstant {
    // @formatter:off
    public static final String CONSUMER_GROUP_PREFIX = "sptalentrv-group-";
    private static final String TOPIC_PREFIX = "sprv-";

    public static final String TOPIC_SE_C_UDP_ORG_INITIALIZE = "udp-org-initialize"; // Demo机构复制第一步
    public static final String TOPIC_PE_C_UDP_ORG_INITIALIZE_STATUS = "udp-org-initialize-status"; // 第一步执行完毕发送回执
    public static final String TOPIC_SE_C_UDP_DEMO_INITIALIZE = "udp-demo-initialize"; // Demo机构复制第二步
    public static final String TOPIC_PSE_C_UDP_DEMO_INITIALIZE_STATUS = "udp-demo-initialize-status"; // 第二步执行完毕发送回执
    public static final String TOPIC_SE_C_UDP_USER_UPDATE = "udp-user-update";
    public static final String TOPIC_SE_C_UDP_DYNAMIC_GROUP_USER_CHANGE = "udp-user-group-user-relation-change";

    public static final String TOPIC_PE_C_TALENT_ADD_GROUP_MEMBER = "talent-add-group-member";

    /* 起点自己的demo机构复制流程 */
    public static final String TOPIC_SE_C_ORG_COPY_STEP_1 = "sp-demo-init-start";
    public static final String TOPIC_SE_C_ORG_COPY_STEP_2 = "sp-demo-init-copy";

    /* 处理资源转移之后，发送结果回执 */
    public static final String TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE = "share-transfer-user-resource";
    public static final String TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE_CALLBACK = "share-transfer-user-resource-callback";

    public static final String TOPIC_SPTALENTRV_EXPORT_FILE = "sptalentrv-export-file";


    public static final String TOPIC_PROFILE_ACTIVITY_CALC = TOPIC_PREFIX + "profile-activity-calc";
    public static final String TOPIC_PERF_ACTIVITY_CALC = TOPIC_PREFIX + "perf-activity-calc";

    /* 证书相关MQ主题 */
    public static final String TOPIC_CER_TEMP_ISSUE_V2 = "cer-temp-issue-v2";
    public static final String CER_ISSUE_STATUS_CHANGE_TOPIC = "cer-issue-status-change-event";

    public static final String ACTV_APPRAISAL_RESET_RESULT = "actv_appraisal_reset_result";

    public static final String TOPIC_AUTH_PRJ_RESULT = TOPIC_PREFIX + "auth-prj-result-zq1";
    public static final String TOPIC_AUTH_PRJ_ADD_USER = TOPIC_PREFIX + "auth-prj-add-user";
    public static final String TOPIC_AUTH_PRJ_DELETE = TOPIC_PREFIX + "auth-prj-delete";

    /* aom学员活动进度发生变化时的回调通知**/
    public static final String TOPIC_MEMBER_STATISTICS_CHANGE = "aom-member-statistics-change";

}
