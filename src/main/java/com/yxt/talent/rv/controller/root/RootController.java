package com.yxt.talent.rv.controller.root;

import com.alibaba.fastjson.JSON;
import com.yxt.common.annotation.Auth;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.calimeet.CaliMeetUserAppService;
import com.yxt.talent.rv.application.democopy.DemoCopyService;
import com.yxt.talent.rv.application.democopy.DemoTableProvider;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppManage;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppService;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.infrastructure.common.utilities.util.jwt.JwtUserInfo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.service.auth.AuthenticateService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import com.yxt.talentbkfacade.service.TalentbkFacade;
import com.yxt.talentrvfacade.service.TalentrvFacade;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.Constants.SEPARATOR_CHAR_ROLE_ALL;
import static com.yxt.common.enums.AuthType.CUSTOM;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNullAndBlank;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/root")
public class RootController {
    private final DemoCopyService demoCopyService;
    private final AuthenticateService authenticateService;
    private final XpdResultCalcService xpdResultCalcService;
    private final XpdMapper xpdMapper;
    private final CaliMeetUserAppService caliMeetUserAppService;
    private final CalimeetMapper calimeetMapper;
    private final XpdGridAppManage xpdGridAppManage;
    private final XpdGridAppService xpdGridAppService;
    private final XpdGridMapper xpdGridMapper;
    private final TalentrvFacade talentrvFacade;
    private final DemoTableProvider demoTableProvider;
    private final SpsdAclServiceImpl spsdAclService;
    private final TalentbkFacade talentbkFacade;

    @Operation(summary = "机构复制手动补数据(全部表)")
    @ResponseStatus(OK)
    @PostMapping(value = "/demo/copy", consumes = MEDIATYPE)
    @Auth(type = {CUSTOM}, codes = {SEPARATOR_CHAR_ROLE_ALL})
    public void copyOrg(@RequestBody OrgInit4Mq orgInit) {
        log.info("LOG10082:{}", bean2Json(orgInit, ALWAYS));
        demoCopyService.preGenIdMap(orgInit);
        demoCopyService.demoCopy(orgInit);
    }

    @Operation(summary = "手动生成jwt")
    @ResponseStatus(OK)
    @PostMapping(value = "/jwt", produces = MEDIATYPE, consumes = MEDIATYPE)
    @Auth(type = {CUSTOM})
    public String genJwt(@RequestBody JwtUserInfo claim) {
        return authenticateService.generateToken(claim);
    }

    @Operation(summary = "机构复制手动补数据(指定表)")
    @ResponseStatus(OK)
    @GetMapping(value = "/demo/orgcustom")
    @Auth(type = {CUSTOM})
    public void demoOrgCustom(@RequestParam String orgId) {
        String udpInit4Mq = talentbkFacade.getUdpInit4Mq(orgId);
        OrgInit4Mq orgInit = JSON.parseObject(udpInit4Mq, OrgInit4Mq.class);

        DemoCopyRunner runner = new DemoCopyRunner();
        runner.initCopyCtx(orgInit);
        demoTableProvider.addOrgCustom(runner);
        log.info("LOG10216:执行复制");
        runner.copyRun();
        log.info("LOG10226:demo copy end");
    }

    /**
     * 第一步：清洗用户维度组结果
     * @param request
     * @param xpdIds
     */
    @Auth(type = CUSTOM)
    @ResponseStatus(OK)
    @PostMapping(value = "/xpd/user/result/dimcomb/refresh", consumes = MEDIATYPE)
    public void refreshUserData(HttpServletRequest request, @RequestBody List<String> xpdIds) {
        if (CollectionUtils.isEmpty(xpdIds)) {
            xpdIds = xpdMapper.select4RefreshUserDimCombResult();
        }
        log.info("LOG20973:refreshUserData xpdIds.size={}", xpdIds.size());
        for (String xpdId : filterNullAndBlank(xpdIds)) {
            try {
                log.debug("LOG20953:xpdId={}", xpdId);
                xpdResultCalcService.refreshUserDimCombResult(xpdId);
            } catch(Exception e) {
                log.warn("LOG20923:{}", xpdId, e);
            }
        }
    }

    /**
     * 第一步：清洗维度分级标准的默认颜色
     */
    @Auth(type = CUSTOM)
    @ResponseStatus(OK)
    @PostMapping(value = "/calimeet/user/thirdDimColor/refresh", consumes = MEDIATYPE)
    public void refreshThirdDimColor(HttpServletRequest request, @RequestParam(value = "isForce", required = false) boolean isForce, @RequestBody List<String> gridIds) {
        if (CollectionUtils.isEmpty(gridIds)) {
            gridIds = xpdGridMapper.select4RefreshThirdDimColor();
        }
        log.info("LOG21263:refreshThirdDimColor gridIds.size={}", gridIds.size());
        for (String gridId : filterNullAndBlank(gridIds)) {
            try {
                log.debug("LOG21283:gridId={}, isForce={}", gridId, isForce);
                xpdGridAppService.refreshThirdDimColor(gridId, isForce);
            } catch(Exception e) {
                log.warn("LOG21273:{}", gridId, e);
            }
        }
    }

    /**
     * 第二步：清洗老的校准会初始结果
     */
    @Auth(type = CUSTOM)
    @ResponseStatus(OK)
    @PostMapping(value = "/calimeet/user/result/refresh", consumes = MEDIATYPE)
    public void refreshUserResult(HttpServletRequest request, @RequestBody List<String> caliMeetIds) {
        if (CollectionUtils.isEmpty(caliMeetIds)) {
            caliMeetIds = calimeetMapper.select4RefreshCaliMeetUserResult();
        }
        log.info("LOG42446:refreshUserResult caliMeetIds.size={}", caliMeetIds.size());
        for (String caliMeetId : filterNullAndBlank(caliMeetIds)) {
            try {
                log.debug("LOG42436:caliMeetId={}", caliMeetId);
                caliMeetUserAppService.refreshCaliMeetUserResult(caliMeetId);
            } catch (Exception e) {
                log.warn("LOG20924:{}", caliMeetId, e);
            }
        }
    }

}
